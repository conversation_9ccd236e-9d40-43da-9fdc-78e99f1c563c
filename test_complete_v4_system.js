// 🧪 اختبار شامل وكامل للنظام v4 الشامل التفصيلي الحقيقي
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testCompleteV4System() {
    console.log('🧪 بدء الاختبار الشامل والكامل للنظام v4 الشامل التفصيلي الحقيقي...');
    console.log('=' .repeat(120));
    
    try {
        // إنشاء مثيل من النظام الحقيقي
        const bugBounty = new BugBountyCore();
        
        // ثغرات اختبار متنوعة
        const testVulnerabilities = [
            {
                name: 'SQL Injection Critical',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'https://example.com/login.php?id=1',
                parameter: 'id',
                payload: "1' OR 1=1 --",
                description: 'ثغرة SQL Injection خطيرة في نظام تسجيل الدخول',
                exploitation_confirmed: true,
                real_test_result: 'تم تأكيد الثغرة - تم الوصول لقاعدة البيانات'
            },
            {
                name: 'Cross-Site Scripting (XSS)',
                type: 'XSS',
                severity: 'High',
                url: 'https://example.com/search.php?q=test',
                parameter: 'q',
                payload: '<script>alert("XSS")</script>',
                description: 'ثغرة XSS في نظام البحث',
                exploitation_confirmed: true,
                real_test_result: 'تم تنفيذ الكود JavaScript بنجاح'
            },
            {
                name: 'File Upload Vulnerability',
                type: 'File Upload',
                severity: 'High',
                url: 'https://example.com/upload.php',
                parameter: 'file',
                payload: 'shell.php',
                description: 'ثغرة رفع ملفات خطيرة',
                exploitation_confirmed: true,
                real_test_result: 'تم رفع ملف PHP shell بنجاح'
            }
        ];

        console.log(`🔥 اختبار النظام v4 مع ${testVulnerabilities.length} ثغرات حقيقية...`);
        console.log('=' .repeat(80));
        
        // 1️⃣ اختبار التقرير الرئيسي الشامل
        console.log('📊 1️⃣ اختبار التقرير الرئيسي الشامل...');
        const mainReport = await bugBounty.generateComprehensiveReport({
            url: 'https://example.com',
            vulnerabilities: testVulnerabilities,
            total_vulnerabilities: testVulnerabilities.length
        });
        
        console.log(`📏 حجم التقرير الرئيسي: ${(mainReport.length / 1024).toFixed(2)} KB`);
        
        // حفظ التقرير الرئيسي
        fs.writeFileSync('test_v4_main_report.html', mainReport, 'utf8');
        console.log('💾 تم حفظ التقرير الرئيسي: test_v4_main_report.html');
        
        // 2️⃣ اختبار التقرير المنفصل
        console.log('📄 2️⃣ اختبار التقرير المنفصل...');
        const separateReport = await bugBounty.formatSinglePageReport({
            page_name: 'اختبار شامل للنظام v4',
            page_url: 'https://example.com',
            vulnerabilities: testVulnerabilities,
            interactive_dialogues: [
                {
                    title: 'حوار تفاعلي شامل',
                    content: 'محتوى الحوار التفاعلي للاختبار الشامل'
                }
            ],
            screenshots: [
                {
                    title: 'لقطة شاشة قبل الاستغلال',
                    url: 'before_test.png',
                    alt: 'حالة النظام قبل الاستغلال'
                },
                {
                    title: 'لقطة شاشة بعد الاستغلال',
                    url: 'after_test.png',
                    alt: 'حالة النظام بعد الاستغلال'
                }
            ]
        });
        
        console.log(`📏 حجم التقرير المنفصل: ${(separateReport.length / 1024).toFixed(2)} KB`);
        
        // حفظ التقرير المنفصل
        fs.writeFileSync('test_v4_separate_report.html', separateReport, 'utf8');
        console.log('💾 تم حفظ التقرير المنفصل: test_v4_separate_report.html');
        
        // 3️⃣ اختبار الدوال الـ36 بالتفصيل
        console.log('🔥 3️⃣ اختبار جميع الدوال الـ36 بالتفصيل...');
        const functionsDisplay = bugBounty.generateComprehensiveFunctionsDisplay();
        console.log(`📊 حجم عرض الدوال الـ36: ${(functionsDisplay.length / 1024).toFixed(2)} KB`);
        
        const functionsCount = (functionsDisplay.match(/Function \d+:/g) || []).length;
        console.log(`🔢 عدد الدوال المعروضة: ${functionsCount}/36`);
        
        // حفظ عرض الدوال
        fs.writeFileSync('test_v4_functions_display.html', `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>عرض الدوال الـ36 الشاملة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .functions-container { background: #f8f9fa; padding: 20px; border-radius: 10px; }
    </style>
</head>
<body>
    <h1>🔥 عرض الدوال الـ36 الشاملة التفصيلية</h1>
    <div class="functions-container">
        ${functionsDisplay}
    </div>
</body>
</html>`, 'utf8');
        console.log('💾 تم حفظ عرض الدوال: test_v4_functions_display.html');
        
        // 4️⃣ اختبار الملفات الشاملة
        console.log('📁 4️⃣ اختبار الملفات الشاملة التفصيلية...');
        const filesDisplay = bugBounty.generateComprehensiveFilesDisplay();
        console.log(`📊 حجم عرض الملفات: ${(filesDisplay.length / 1024).toFixed(2)} KB`);
        
        const filesCount = (filesDisplay.match(/\.js|\.html|\.css|\.json/g) || []).length;
        console.log(`📁 عدد الملفات المعروضة: ${filesCount}`);
        
        // حفظ عرض الملفات
        fs.writeFileSync('test_v4_files_display.html', `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>عرض الملفات الشاملة التفصيلية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .files-container { background: #f8f9fa; padding: 20px; border-radius: 10px; }
    </style>
</head>
<body>
    <h1>📁 عرض الملفات الشاملة التفصيلية</h1>
    <div class="files-container">
        ${filesDisplay}
    </div>
</body>
</html>`, 'utf8');
        console.log('💾 تم حفظ عرض الملفات: test_v4_files_display.html');
        
        // 5️⃣ فحص القالب الشامل والتنسيق
        console.log('🎨 5️⃣ فحص القالب الشامل والتنسيق...');
        
        // فحص التنسيق في التقرير الرئيسي
        const formatChecks = {
            hasCSS: mainReport.includes('<style>') || mainReport.includes('linear-gradient'),
            hasGradients: mainReport.includes('linear-gradient(135deg'),
            hasBorders: mainReport.includes('border-radius'),
            hasColors: mainReport.includes('color:') && mainReport.includes('#'),
            hasSections: mainReport.includes('.section') || mainReport.includes('class="section'),
            hasHeaders: mainReport.includes('<h1>') || mainReport.includes('<h2>'),
            hasStructure: mainReport.includes('<div') && mainReport.includes('class='),
            hasRTL: mainReport.includes('dir="rtl"') || mainReport.includes('direction: rtl'),
            hasTemplate: mainReport.includes('<!DOCTYPE html>') && mainReport.includes('<html'),
            hasComprehensiveTemplate: mainReport.includes('القالب الشامل') || mainReport.includes('comprehensive')
        };
        
        console.log('🎨 نتائج فحص التنسيق:');
        Object.entries(formatChecks).forEach(([key, value]) => {
            const checkName = {
                hasCSS: 'CSS والأنماط',
                hasGradients: 'التدرجات اللونية',
                hasBorders: 'الحدود المدورة',
                hasColors: 'الألوان',
                hasSections: 'الأقسام',
                hasHeaders: 'العناوين',
                hasStructure: 'بنية HTML',
                hasRTL: 'دعم العربية RTL',
                hasTemplate: 'قالب HTML',
                hasComprehensiveTemplate: 'القالب الشامل'
            }[key] || key;
            console.log(`   ${checkName}: ${value ? '✅ موجود' : '❌ مفقود'}`);
        });
        
        // 6️⃣ فحص ترتيب الأقسام
        console.log('📋 6️⃣ فحص ترتيب الأقسام...');
        const sectionOrder = [
            'الملخص التنفيذي',
            'تحليل التأثير',
            'الثغرات المكتشفة',
            'التغيرات البصرية',
            'الحوار التفاعلي',
            'التوصيات'
        ];
        
        let orderCorrect = true;
        let lastIndex = -1;
        sectionOrder.forEach((section, index) => {
            const currentIndex = mainReport.indexOf(section);
            if (currentIndex > lastIndex && currentIndex !== -1) {
                console.log(`   ${index + 1}. ${section}: ✅ في الترتيب الصحيح`);
                lastIndex = currentIndex;
            } else {
                console.log(`   ${index + 1}. ${section}: ❌ ترتيب خاطئ أو مفقود`);
                orderCorrect = false;
            }
        });
        
        // 7️⃣ فحص محتوى الدوال والملفات
        console.log('🔍 7️⃣ فحص محتوى الدوال والملفات...');
        
        // فحص وجود الدوال الـ36 في التقرير
        const functionsInReport = (mainReport.match(/Function \d+:/g) || []).length;
        const filesInReport = (mainReport.match(/\.js|\.html|\.css|\.json/g) || []).length;
        
        console.log(`🔢 الدوال في التقرير الرئيسي: ${functionsInReport}/36`);
        console.log(`📁 الملفات في التقرير الرئيسي: ${filesInReport}`);
        
        // فحص المحتوى الديناميكي
        const dynamicContentChecks = {
            hasVulnerabilityContent: mainReport.includes('SQL Injection') && mainReport.includes('XSS'),
            hasRealData: mainReport.includes('البيانات الحقيقية'),
            hasExploitationSteps: mainReport.includes('خطوات الاستغلال'),
            hasImpactAnalysis: mainReport.includes('تحليل التأثير'),
            hasRecommendations: mainReport.includes('التوصيات'),
            hasScreenshots: mainReport.includes('لقطة شاشة') || mainReport.includes('screenshot')
        };
        
        console.log('🔍 فحص المحتوى الديناميكي:');
        Object.entries(dynamicContentChecks).forEach(([key, value]) => {
            const contentName = {
                hasVulnerabilityContent: 'محتوى الثغرات',
                hasRealData: 'البيانات الحقيقية',
                hasExploitationSteps: 'خطوات الاستغلال',
                hasImpactAnalysis: 'تحليل التأثير',
                hasRecommendations: 'التوصيات',
                hasScreenshots: 'لقطات الشاشة'
            }[key] || key;
            console.log(`   ${contentName}: ${value ? '✅ موجود' : '❌ مفقود'}`);
        });
        
        // 8️⃣ حساب النتائج النهائية
        console.log('📊 8️⃣ حساب النتائج النهائية...');
        
        const formatScore = Object.values(formatChecks).filter(Boolean).length / Object.keys(formatChecks).length * 100;
        const orderScore = orderCorrect ? 100 : 0;
        const contentScore = Object.values(dynamicContentChecks).filter(Boolean).length / Object.keys(dynamicContentChecks).length * 100;
        const functionsScore = (functionsInReport / 36) * 100;
        const filesScore = filesInReport > 30 ? 100 : (filesInReport / 30) * 100;
        
        const overallScore = (formatScore + orderScore + contentScore + functionsScore + filesScore) / 5;
        
        console.log('📊 النتائج التفصيلية:');
        console.log(`   🎨 التنسيق والقالب: ${formatScore.toFixed(1)}%`);
        console.log(`   📋 ترتيب الأقسام: ${orderScore.toFixed(1)}%`);
        console.log(`   🔍 المحتوى الديناميكي: ${contentScore.toFixed(1)}%`);
        console.log(`   🔢 الدوال الـ36: ${functionsScore.toFixed(1)}%`);
        console.log(`   📁 الملفات الشاملة: ${filesScore.toFixed(1)}%`);
        console.log(`   🏆 النتيجة الإجمالية: ${overallScore.toFixed(1)}%`);
        
        // 9️⃣ إنشاء تقرير الاختبار الشامل
        console.log('📋 9️⃣ إنشاء تقرير الاختبار الشامل...');
        
        const testReport = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير الاختبار الشامل للنظام v4</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .score { font-size: 1.2em; font-weight: bold; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .score.excellent { background: #d4edda; color: #155724; }
        .score.good { background: #fff3cd; color: #856404; }
        .score.poor { background: #f8d7da; color: #721c24; }
        .section { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .file-link { display: inline-block; margin: 10px; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .file-link:hover { background: #0056b3; }
        .check-item { margin: 8px 0; padding: 8px; border-radius: 4px; }
        .check-item.pass { background: #d4edda; border-left: 4px solid #28a745; }
        .check-item.fail { background: #f8d7da; border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #007bff;">🧪 تقرير الاختبار الشامل للنظام v4 الشامل التفصيلي</h1>
        
        <div class="section">
            <h2>📊 النتائج الإجمالية</h2>
            <div class="score ${overallScore >= 80 ? 'excellent' : overallScore >= 60 ? 'good' : 'poor'}">
                🏆 النتيجة الإجمالية: ${overallScore.toFixed(1)}%
            </div>
            <p><strong>عدد الثغرات المختبرة:</strong> ${testVulnerabilities.length}</p>
            <p><strong>حجم التقرير الرئيسي:</strong> ${(mainReport.length / 1024).toFixed(2)} KB</p>
            <p><strong>حجم التقرير المنفصل:</strong> ${(separateReport.length / 1024).toFixed(2)} KB</p>
        </div>
        
        <div class="section">
            <h2>📋 النتائج التفصيلية</h2>
            <div class="score ${formatScore >= 80 ? 'excellent' : formatScore >= 60 ? 'good' : 'poor'}">
                🎨 التنسيق والقالب: ${formatScore.toFixed(1)}%
            </div>
            <div class="score ${orderScore >= 80 ? 'excellent' : orderScore >= 60 ? 'good' : 'poor'}">
                📋 ترتيب الأقسام: ${orderScore.toFixed(1)}%
            </div>
            <div class="score ${contentScore >= 80 ? 'excellent' : contentScore >= 60 ? 'good' : 'poor'}">
                🔍 المحتوى الديناميكي: ${contentScore.toFixed(1)}%
            </div>
            <div class="score ${functionsScore >= 80 ? 'excellent' : functionsScore >= 60 ? 'good' : 'poor'}">
                🔢 الدوال الـ36: ${functionsScore.toFixed(1)}% (${functionsInReport}/36)
            </div>
            <div class="score ${filesScore >= 80 ? 'excellent' : filesScore >= 60 ? 'good' : 'poor'}">
                📁 الملفات الشاملة: ${filesScore.toFixed(1)}% (${filesInReport} ملف)
            </div>
        </div>
        
        <div class="section">
            <h2>🎨 فحص التنسيق والقالب</h2>
            ${Object.entries(formatChecks).map(([key, value]) => {
                const checkName = {
                    hasCSS: 'CSS والأنماط',
                    hasGradients: 'التدرجات اللونية',
                    hasBorders: 'الحدود المدورة',
                    hasColors: 'الألوان',
                    hasSections: 'الأقسام',
                    hasHeaders: 'العناوين',
                    hasStructure: 'بنية HTML',
                    hasRTL: 'دعم العربية RTL',
                    hasTemplate: 'قالب HTML',
                    hasComprehensiveTemplate: 'القالب الشامل'
                }[key] || key;
                return `<div class="check-item ${value ? 'pass' : 'fail'}">${checkName}: ${value ? '✅ موجود' : '❌ مفقود'}</div>`;
            }).join('')}
        </div>
        
        <div class="section">
            <h2>🔍 فحص المحتوى الديناميكي</h2>
            ${Object.entries(dynamicContentChecks).map(([key, value]) => {
                const contentName = {
                    hasVulnerabilityContent: 'محتوى الثغرات',
                    hasRealData: 'البيانات الحقيقية',
                    hasExploitationSteps: 'خطوات الاستغلال',
                    hasImpactAnalysis: 'تحليل التأثير',
                    hasRecommendations: 'التوصيات',
                    hasScreenshots: 'لقطات الشاشة'
                }[key] || key;
                return `<div class="check-item ${value ? 'pass' : 'fail'}">${contentName}: ${value ? '✅ موجود' : '❌ مفقود'}</div>`;
            }).join('')}
        </div>
        
        <div class="section">
            <h2>📁 الملفات المُنشأة</h2>
            <a href="test_v4_main_report.html" class="file-link">📊 التقرير الرئيسي</a>
            <a href="test_v4_separate_report.html" class="file-link">📄 التقرير المنفصل</a>
            <a href="test_v4_functions_display.html" class="file-link">🔥 عرض الدوال الـ36</a>
            <a href="test_v4_files_display.html" class="file-link">📁 عرض الملفات الشاملة</a>
        </div>
        
        <div class="section">
            <h2>🎯 الخلاصة</h2>
            <p><strong>حالة النظام:</strong> ${overallScore >= 80 ? '✅ ممتاز - النظام يعمل بشكل مثالي' : overallScore >= 60 ? '⚠️ جيد - يحتاج تحسينات طفيفة' : '❌ يحتاج إصلاحات'}</p>
            <p><strong>التوصية:</strong> ${overallScore >= 80 ? 'النظام جاهز للاستخدام الإنتاجي' : 'يُنصح بمراجعة النقاط المفقودة'}</p>
        </div>
    </div>
</body>
</html>`;
        
        fs.writeFileSync('test_v4_comprehensive_report.html', testReport, 'utf8');
        console.log('💾 تم حفظ تقرير الاختبار الشامل: test_v4_comprehensive_report.html');
        
        // 🔟 ملخص النتائج
        console.log('');
        console.log('🎯 ملخص النتائج النهائية:');
        console.log('=' .repeat(80));
        console.log(`🏆 النتيجة الإجمالية: ${overallScore.toFixed(1)}%`);
        console.log(`📊 التقرير الرئيسي: ${(mainReport.length / 1024).toFixed(2)} KB`);
        console.log(`📄 التقرير المنفصل: ${(separateReport.length / 1024).toFixed(2)} KB`);
        console.log(`🔢 الدوال الـ36: ${functionsInReport}/36 (${functionsScore.toFixed(1)}%)`);
        console.log(`📁 الملفات الشاملة: ${filesInReport} ملف`);
        console.log(`🎨 التنسيق والقالب: ${formatScore.toFixed(1)}%`);
        console.log(`📋 ترتيب الأقسام: ${orderScore.toFixed(1)}%`);
        console.log(`🔍 المحتوى الديناميكي: ${contentScore.toFixed(1)}%`);
        
        console.log('');
        console.log('📁 الملفات المُنشأة:');
        console.log('   📊 test_v4_main_report.html - التقرير الرئيسي');
        console.log('   📄 test_v4_separate_report.html - التقرير المنفصل');
        console.log('   🔥 test_v4_functions_display.html - عرض الدوال الـ36');
        console.log('   📁 test_v4_files_display.html - عرض الملفات الشاملة');
        console.log('   📋 test_v4_comprehensive_report.html - تقرير الاختبار الشامل');
        
        return {
            success: overallScore >= 80,
            overallScore: overallScore,
            mainReportSize: mainReport.length,
            separateReportSize: separateReport.length,
            functionsCount: functionsInReport,
            filesCount: filesInReport,
            formatScore: formatScore,
            orderScore: orderScore,
            contentScore: contentScore,
            filesGenerated: [
                'test_v4_main_report.html',
                'test_v4_separate_report.html',
                'test_v4_functions_display.html',
                'test_v4_files_display.html',
                'test_v4_comprehensive_report.html'
            ]
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار الشامل:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار الشامل
testCompleteV4System()
    .then(result => {
        console.log('');
        console.log('🎉 انتهى الاختبار الشامل للنظام v4!');
        console.log(`🏆 النتيجة النهائية: ${result.success ? 'نجح ✅' : 'فشل ❌'}`);
        if (result.overallScore) {
            console.log(`📊 النتيجة الإجمالية: ${result.overallScore.toFixed(1)}%`);
        }
        if (result.error) {
            console.log(`❌ خطأ: ${result.error}`);
        }
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ خطأ فادح في الاختبار الشامل:', error);
        process.exit(1);
    });
