// اختبار سريع للتأكد من إصلاح الأخطاء
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testQuickFix() {
    console.log('🔧 اختبار سريع لإصلاح الأخطاء...');
    
    try {
        const core = new BugBountyCore();
        console.log('✅ تم إنشاء مثيل BugBountyCore بنجاح');
        
        // اختبار ثغرة واحدة فقط
        const testVuln = {
            name: 'SQL Injection Test',
            type: 'sql injection',
            severity: 'Critical',
            url: 'https://test.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --",
            response: 'تم تسجيل الدخول بنجاح',
            method: 'POST',
            timestamp: new Date().toISOString(),
            confidence: 95,
            cvss_score: 9.8
        };
        
        console.log('🎯 اختبار تطبيق الدوال الـ36...');
        await core.applyAllComprehensiveFunctionsToVulnerability(testVuln, {});
        console.log('✅ تم تطبيق جميع الدوال بنجاح بدون أخطاء!');
        
        console.log('📊 اختبار إنشاء التقرير...');
        const reportData = {
            vulnerabilities: [testVuln],
            scan_info: {
                scan_id: 'test_quick_fix',
                timestamp: new Date().toISOString(),
                total_vulnerabilities: 1,
                scan_type: 'quick_test',
                system_version: 'Bug Bounty v4.0'
            },
            summary: { critical: 1, high: 0, medium: 0, low: 0, total: 1 }
        };
        
        const report = await core.generateMainReport(reportData);
        console.log(`✅ تم إنشاء التقرير بنجاح! الحجم: ${(report.length / 1024).toFixed(2)} KB`);
        
        console.log('🎉 جميع الاختبارات نجحت! الأخطاء تم إصلاحها.');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return false;
    }
}

testQuickFix().then(success => {
    if (success) {
        console.log('🏆 النتيجة: تم إصلاح جميع الأخطاء بنجاح!');
    } else {
        console.log('⚠️ النتيجة: لا تزال هناك أخطاء تحتاج إصلاح');
    }
});
