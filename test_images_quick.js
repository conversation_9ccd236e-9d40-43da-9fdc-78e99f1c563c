// اختبار سريع للصور في التقارير
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

console.log('🚀 اختبار سريع للصور...');

// إنشاء مثيل النظام
const bugBountyCore = new BugBountyCore();

// إنشاء ثغرة اختبار
const testVuln = {
    name: 'SQL Injection Test',
    type: 'SQL Injection',
    severity: 'high'
};

console.log('📸 اختبار إنشاء الصور المضمنة...');

// اختبار دالة إنشاء الصور المضمنة
const beforeImage = bugBountyCore.generateInlineImage(testVuln.name, testVuln.type, 'before');
const duringImage = bugBountyCore.generateInlineImage(testVuln.name, testVuln.type, 'during');
const afterImage = bugBountyCore.generateInlineImage(testVuln.name, testVuln.type, 'after');

console.log('✅ تم إنشاء الصور بنجاح!');
console.log(`📊 حجم صورة "قبل": ${beforeImage.length} حرف`);
console.log(`📊 حجم صورة "أثناء": ${duringImage.length} حرف`);
console.log(`📊 حجم صورة "بعد": ${afterImage.length} حرف`);

// إنشاء تقرير HTML بسيط مع الصور
const htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور - Bug Bounty v4.0</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .screenshots-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
        .screenshot-item { text-align: center; }
        .screenshot-item img { max-width: 100%; height: 200px; object-fit: cover; border-radius: 8px; border: 2px solid #ddd; }
        .screenshot-item p { margin-top: 10px; font-weight: bold; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار الصور - Bug Bounty v4.0</h1>
        
        <h2>📸 لقطات الشاشة للثغرة: ${testVuln.name}</h2>
        
        <div class="screenshots-grid">
            <div class="screenshot-item">
                <img src="${beforeImage}" alt="قبل الاستغلال">
                <p>قبل الاستغلال</p>
            </div>
            <div class="screenshot-item">
                <img src="${duringImage}" alt="أثناء الاستغلال">
                <p>أثناء الاستغلال</p>
            </div>
            <div class="screenshot-item">
                <img src="${afterImage}" alt="بعد الاستغلال">
                <p>بعد الاستغلال</p>
            </div>
        </div>
        
        <div style="background: #e9ecef; padding: 20px; border-radius: 10px; margin-top: 30px;">
            <h3>📊 معلومات الاختبار:</h3>
            <ul>
                <li><strong>نوع الثغرة:</strong> ${testVuln.type}</li>
                <li><strong>الخطورة:</strong> ${testVuln.severity}</li>
                <li><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar-SA')}</li>
                <li><strong>حالة الصور:</strong> مضمنة كـ base64</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

// حفظ التقرير
const fs = require('fs');
const reportPath = `image_test_report_${Date.now()}.html`;
fs.writeFileSync(reportPath, htmlContent, 'utf8');

console.log(`✅ تم إنشاء تقرير اختبار الصور: ${reportPath}`);
console.log('🎉 يمكنك الآن فتح التقرير في المتصفح لرؤية الصور!');
