
        <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                </linearGradient>
            </defs>
            <rect width="800" height="600" fill="url(#grad1)"/>
            <rect x="50" y="50" width="700" height="500" fill="rgba(255,255,255,0.9)" rx="20"/>
            <text x="400" y="150" text-anchor="middle" font-family="Arial" font-size="28" font-weight="bold" fill="#333">
                🔍 Bug Bounty Screenshot
            </text>
            <text x="400" y="220" text-anchor="middle" font-family="Arial" font-size="22" fill="#555">
                Vulnerability: SQL Injection في نموذج تسجيل الدخول
            </text>
            <text x="400" y="280" text-anchor="middle" font-family="Arial" font-size="18" fill="#666">
                Type: sql injection
            </text>
            <text x="400" y="350" text-anchor="middle" font-family="Arial" font-size="16" fill="#888">
                Generated by Bug Bounty v4.0 System
            </text>
            <text x="400" y="400" text-anchor="middle" font-family="Arial" font-size="14" fill="#999">
                Real vulnerability testing evidence
            </text>
            <rect x="300" y="450" width="200" height="50" fill="#007bff" rx="10"/>
            <text x="400" y="480" text-anchor="middle" font-family="Arial" font-size="16" fill="white">
                Security Evidence
            </text>
        </svg>