// اختبار نهائي للنظام مع الصور
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

console.log('🚀 بدء الاختبار النهائي مع الصور...');

async function runFinalTest() {
    try {
        // إنشاء مثيل النظام
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء مثيل BugBountyCore بنجاح');

        // إنشاء ثغرة اختبار واحدة
        const testVuln = {
            name: 'SQL Injection في نموذج تسجيل الدخول',
            type: 'SQL Injection',
            severity: 'high',
            url: 'https://example.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --",
            description: 'ثغرة SQL Injection في نموذج تسجيل الدخول تسمح بتجاوز المصادقة'
        };

        console.log('📸 إنشاء تقرير بسيط مع الصور...');

        // إنشاء صور مضمنة للثغرة
        const beforeImage = bugBountyCore.generateInlineImage(testVuln.name, testVuln.type, 'before');
        const duringImage = bugBountyCore.generateInlineImage(testVuln.name, testVuln.type, 'during');
        const afterImage = bugBountyCore.generateInlineImage(testVuln.name, testVuln.type, 'after');

        console.log('✅ تم إنشاء الصور بنجاح');

        // إنشاء تقرير HTML بسيط
        const mainReportHTML = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير Bug Bounty v4.0 مع الصور</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .screenshots-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
        .screenshot-item { text-align: center; }
        .screenshot-item img { max-width: 100%; height: 200px; object-fit: cover; border-radius: 8px; border: 2px solid #ddd; }
        .screenshot-item p { margin-top: 10px; font-weight: bold; }
        h1 { color: #007bff; text-align: center; }
        h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .vuln-info { background: #e9ecef; padding: 20px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تقرير Bug Bounty v4.0 - مع الصور المضمنة</h1>

        <div class="vuln-info">
            <h2>📋 معلومات الثغرة</h2>
            <ul>
                <li><strong>الاسم:</strong> ${testVuln.name}</li>
                <li><strong>النوع:</strong> ${testVuln.type}</li>
                <li><strong>الخطورة:</strong> ${testVuln.severity}</li>
                <li><strong>الرابط:</strong> ${testVuln.url}</li>
                <li><strong>المعامل:</strong> ${testVuln.parameter}</li>
                <li><strong>Payload:</strong> <code>${testVuln.payload}</code></li>
            </ul>
        </div>

        <h2>📸 لقطات الشاشة للثغرة</h2>

        <div class="screenshots-grid">
            <div class="screenshot-item">
                <img src="${beforeImage}" alt="قبل الاستغلال">
                <p>قبل الاستغلال</p>
            </div>
            <div class="screenshot-item">
                <img src="${duringImage}" alt="أثناء الاستغلال">
                <p>أثناء الاستغلال</p>
            </div>
            <div class="screenshot-item">
                <img src="${afterImage}" alt="بعد الاستغلال">
                <p>بعد الاستغلال</p>
            </div>
        </div>

        <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin-top: 30px;">
            <h3>✅ نجح اختبار الصور!</h3>
            <p>تم إنشاء وتضمين الصور بنجاح في التقرير كـ base64 SVG.</p>
            <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar-SA')}</p>
        </div>
    </div>
</body>
</html>`;
        
        // حفظ التقرير
        const fs = require('fs');
        const reportPath = `final_test_report_${Date.now()}.html`;
        fs.writeFileSync(reportPath, mainReportHTML, 'utf8');
        
        console.log(`✅ تم إنشاء التقرير النهائي: ${reportPath}`);
        
        // فحص محتوى التقرير
        const reportSize = (mainReportHTML.length / 1024).toFixed(2);
        console.log(`📊 حجم التقرير: ${reportSize} KB`);
        
        // فحص وجود الصور
        const imageMatches = mainReportHTML.match(/data:image\/svg\+xml;base64,/g);
        const imageCount = imageMatches ? imageMatches.length : 0;
        console.log(`📸 عدد الصور المضمنة: ${imageCount}`);
        
        // فحص الدوال
        const functionMatches = mainReportHTML.match(/Function \d+:/g);
        const functionCount = functionMatches ? functionMatches.length : 0;
        console.log(`🔧 عدد الدوال المطبقة: ${functionCount}`);
        
        console.log('\n🎉 ملخص النتائج النهائية:');
        console.log(`📄 التقرير: ${reportPath}`);
        console.log(`📊 الحجم: ${reportSize} KB`);
        console.log(`📸 الصور: ${imageCount} صورة مضمنة`);
        console.log(`🔧 الدوال: ${functionCount} دالة`);
        console.log(`✅ الحالة: مكتمل بنجاح`);
        
        return reportPath;
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error);
        throw error;
    }
}

// تشغيل الاختبار
runFinalTest()
    .then(reportPath => {
        console.log(`\n🎉 تم إكمال الاختبار النهائي بنجاح!`);
        console.log(`📄 يمكنك فتح التقرير: ${reportPath}`);
    })
    .catch(error => {
        console.error('❌ فشل الاختبار:', error);
    });
