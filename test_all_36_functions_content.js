// 🧪 اختبار شامل لعرض محتوى جميع الدوال الـ36 والملفات الشاملة بالكامل
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testAll36FunctionsContent() {
    console.log('🧪 بدء اختبار عرض محتوى جميع الدوال الـ36 والملفات الشاملة بالكامل...');
    console.log('=' .repeat(100));
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // ثغرة اختبار واحدة للتركيز على المحتوى
        const testVulnerability = {
            name: 'SQL Injection اختبار شامل',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://example.com/test.php?id=1',
            parameter: 'id',
            payload: "1' OR 1=1 --",
            description: 'ثغرة SQL Injection للاختبار الشامل',
            exploitation_confirmed: true,
            real_test_result: 'تم تأكيد الثغرة بنجاح'
        };

        console.log('🔥 اختبار الدوال الـ36 الأساسية بالتفصيل...');
        console.log('-' .repeat(80));
        
        // استخراج البيانات الحقيقية
        const realData = bugBounty.extractRealDataFromDiscoveredVulnerability ? 
            bugBounty.extractRealDataFromDiscoveredVulnerability(testVulnerability) : {};
        
        console.log('📊 البيانات الحقيقية المستخرجة:');
        console.log(JSON.stringify(realData, null, 2));
        console.log('');
        
        // اختبار كل دالة من الدوال الـ36 بالتفصيل
        const functions36Results = {};
        
        console.log('🔍 اختبار الدوال الأساسية (1-6):');
        console.log('-' .repeat(60));
        
        // Function 1: generateComprehensiveDetailsFromRealData
        if (bugBounty.generateComprehensiveDetailsFromRealData) {
            console.log('🔥 Function 1: generateComprehensiveDetailsFromRealData');
            const result1 = await bugBounty.generateComprehensiveDetailsFromRealData(testVulnerability, realData);
            functions36Results.function1 = result1;
            console.log(`   📏 حجم المحتوى: ${result1 ? result1.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result1 ? result1.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 2: generateDynamicImpactForAnyVulnerability
        if (bugBounty.generateDynamicImpactForAnyVulnerability) {
            console.log('🔥 Function 2: generateDynamicImpactForAnyVulnerability');
            const result2 = await bugBounty.generateDynamicImpactForAnyVulnerability(testVulnerability, realData);
            functions36Results.function2 = result2;
            console.log(`   📏 حجم المحتوى: ${result2 ? result2.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result2 ? result2.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 3: generateRealExploitationStepsForVulnerabilityComprehensive
        if (bugBounty.generateRealExploitationStepsForVulnerabilityComprehensive) {
            console.log('🔥 Function 3: generateRealExploitationStepsForVulnerabilityComprehensive');
            const result3 = await bugBounty.generateRealExploitationStepsForVulnerabilityComprehensive(testVulnerability, realData);
            functions36Results.function3 = result3;
            console.log(`   📏 حجم المحتوى: ${result3 ? result3.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result3 ? result3.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 4: generateDynamicRecommendationsForVulnerability
        if (bugBounty.generateDynamicRecommendationsForVulnerability) {
            console.log('🔥 Function 4: generateDynamicRecommendationsForVulnerability');
            const result4 = await bugBounty.generateDynamicRecommendationsForVulnerability(testVulnerability, realData);
            functions36Results.function4 = result4;
            console.log(`   📏 حجم المحتوى: ${result4 ? result4.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result4 ? result4.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 5: generateComprehensiveRiskAnalysis
        if (bugBounty.generateComprehensiveRiskAnalysis) {
            console.log('🔥 Function 5: generateComprehensiveRiskAnalysis');
            const result5 = await bugBounty.generateComprehensiveRiskAnalysis(testVulnerability, realData);
            functions36Results.function5 = result5;
            console.log(`   📏 حجم المحتوى: ${result5 ? result5.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result5 ? result5.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 6: generateAdvancedExploitationTechniques
        if (bugBounty.generateAdvancedExploitationTechniques) {
            console.log('🔥 Function 6: generateAdvancedExploitationTechniques');
            const result6 = await bugBounty.generateAdvancedExploitationTechniques(testVulnerability, realData);
            functions36Results.function6 = result6;
            console.log(`   📏 حجم المحتوى: ${result6 ? result6.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result6 ? result6.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        console.log('🔍 اختبار الدوال المتقدمة (7-12):');
        console.log('-' .repeat(60));
        
        // Function 7: generateRealTimeVulnerabilityAssessment
        if (bugBounty.generateRealTimeVulnerabilityAssessment) {
            console.log('🔥 Function 7: generateRealTimeVulnerabilityAssessment');
            const result7 = await bugBounty.generateRealTimeVulnerabilityAssessment(testVulnerability, realData);
            functions36Results.function7 = result7;
            console.log(`   📏 حجم المحتوى: ${result7 ? result7.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result7 ? result7.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 8: generateComprehensiveSecurityAssessment
        if (bugBounty.generateComprehensiveSecurityAssessment) {
            console.log('🔥 Function 8: generateComprehensiveSecurityAssessment');
            const result8 = await bugBounty.generateComprehensiveSecurityAssessment(testVulnerability, realData);
            functions36Results.function8 = result8;
            console.log(`   📏 حجم المحتوى: ${result8 ? result8.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result8 ? result8.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 9: generateDynamicThreatModelingForVulnerability
        if (bugBounty.generateDynamicThreatModelingForVulnerability) {
            console.log('🔥 Function 9: generateDynamicThreatModelingForVulnerability');
            const result9 = await bugBounty.generateDynamicThreatModelingForVulnerability(testVulnerability, realData);
            functions36Results.function9 = result9;
            console.log(`   📏 حجم المحتوى: ${result9 ? result9.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result9 ? result9.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        // Function 10: generateDynamicVulnerabilityPrioritization
        if (bugBounty.generateDynamicVulnerabilityPrioritization) {
            console.log('🔥 Function 10: generateDynamicVulnerabilityPrioritization');
            const result10 = await bugBounty.generateDynamicVulnerabilityPrioritization(testVulnerability, realData);
            functions36Results.function10 = result10;
            console.log(`   📏 حجم المحتوى: ${result10 ? result10.length : 0} حرف`);
            console.log(`   📝 المحتوى (أول 200 حرف): ${result10 ? result10.substring(0, 200) + '...' : 'غير متاح'}`);
            console.log('');
        }
        
        console.log('🔍 اختبار عرض الدوال الـ36 في القالب:');
        console.log('-' .repeat(60));
        
        // اختبار عرض الدوال الـ36 في القالب
        const functionsDisplay = bugBounty.generateComprehensiveFunctionsDisplay();
        console.log(`📏 حجم عرض الدوال الـ36: ${functionsDisplay ? functionsDisplay.length : 0} حرف`);
        
        // عد الدوال في العرض
        const functionsInDisplay = (functionsDisplay.match(/Function \d+:/g) || []).length;
        console.log(`🔢 عدد الدوال في العرض: ${functionsInDisplay}/36`);
        
        // فحص وجود كل دالة
        const functionNames = [
            'generateComprehensiveDetailsFromRealData',
            'generateDynamicImpactForAnyVulnerability',
            'generateRealExploitationStepsForVulnerabilityComprehensive',
            'generateDynamicRecommendationsForVulnerability',
            'generateComprehensiveRiskAnalysis',
            'generateAdvancedExploitationTechniques',
            'generateRealTimeVulnerabilityAssessment',
            'generateComprehensiveSecurityAssessment',
            'generateDynamicThreatModelingForVulnerability',
            'generateDynamicVulnerabilityPrioritization'
        ];
        
        console.log('🔍 فحص وجود الدوال في العرض:');
        functionNames.forEach((funcName, index) => {
            const exists = functionsDisplay.includes(funcName);
            console.log(`   Function ${index + 1}: ${funcName.substring(0, 40)}... - ${exists ? '✅ موجود' : '❌ مفقود'}`);
        });
        
        console.log('');
        console.log('🔍 اختبار الملفات الشاملة التفصيلية:');
        console.log('-' .repeat(60));
        
        // اختبار عرض الملفات الشاملة
        const filesDisplay = bugBounty.generateComprehensiveFilesDisplay();
        console.log(`📏 حجم عرض الملفات الشاملة: ${filesDisplay ? filesDisplay.length : 0} حرف`);
        
        // عد الملفات في العرض
        const filesInDisplay = (filesDisplay.match(/\.js|\.html|\.css|\.json/g) || []).length;
        console.log(`📁 عدد الملفات في العرض: ${filesInDisplay}`);
        
        // حفظ النتائج في ملف تفصيلي
        const detailedResults = {
            timestamp: new Date().toISOString(),
            vulnerability: testVulnerability,
            realData: realData,
            functions36Results: functions36Results,
            functionsDisplay: {
                content: functionsDisplay,
                size: functionsDisplay ? functionsDisplay.length : 0,
                functionsCount: functionsInDisplay
            },
            filesDisplay: {
                content: filesDisplay,
                size: filesDisplay ? filesDisplay.length : 0,
                filesCount: filesInDisplay
            }
        };
        
        // حفظ النتائج التفصيلية
        fs.writeFileSync('test_36_functions_detailed_results.json', JSON.stringify(detailedResults, null, 2), 'utf8');
        console.log('💾 تم حفظ النتائج التفصيلية: test_36_functions_detailed_results.json');
        
        // إنشاء تقرير HTML مع جميع الدوال
        const htmlReport = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار الدوال الـ36 الشاملة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .function-result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .function-title { color: #007bff; font-weight: bold; }
        .content-preview { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .size-info { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>🧪 نتائج اختبار الدوال الـ36 الشاملة</h1>
    <p><strong>الثغرة المختبرة:</strong> ${testVulnerability.name}</p>
    <p><strong>وقت الاختبار:</strong> ${new Date().toLocaleString('ar')}</p>
    
    <h2>📊 نتائج الدوال:</h2>
    ${Object.entries(functions36Results).map(([key, value]) => `
        <div class="function-result">
            <div class="function-title">${key}</div>
            <div class="size-info">حجم المحتوى: ${value ? value.length : 0} حرف</div>
            <div class="content-preview">${value ? value.substring(0, 500) + (value.length > 500 ? '...' : '') : 'غير متاح'}</div>
        </div>
    `).join('')}
    
    <h2>📂 عرض الدوال الـ36:</h2>
    <div class="size-info">حجم العرض: ${functionsDisplay ? functionsDisplay.length : 0} حرف</div>
    <div class="size-info">عدد الدوال: ${functionsInDisplay}/36</div>
    <div class="content-preview">${functionsDisplay || 'غير متاح'}</div>
    
    <h2>📁 عرض الملفات الشاملة:</h2>
    <div class="size-info">حجم العرض: ${filesDisplay ? filesDisplay.length : 0} حرف</div>
    <div class="size-info">عدد الملفات: ${filesInDisplay}</div>
    <div class="content-preview">${filesDisplay || 'غير متاح'}</div>
</body>
</html>`;
        
        fs.writeFileSync('test_36_functions_report.html', htmlReport, 'utf8');
        console.log('💾 تم حفظ التقرير HTML: test_36_functions_report.html');
        
        console.log('');
        console.log('🎯 ملخص النتائج:');
        console.log('=' .repeat(80));
        console.log(`📊 عدد الدوال المختبرة: ${Object.keys(functions36Results).length}`);
        console.log(`📏 إجمالي حجم المحتوى: ${Object.values(functions36Results).reduce((total, content) => total + (content ? content.length : 0), 0)} حرف`);
        console.log(`🔢 الدوال في العرض: ${functionsInDisplay}/36`);
        console.log(`📁 الملفات في العرض: ${filesInDisplay}`);
        
        const allFunctionsWorking = Object.values(functions36Results).every(result => result && result.length > 0);
        console.log(`🏆 حالة الدوال: ${allFunctionsWorking ? '✅ جميع الدوال تعمل' : '❌ بعض الدوال لا تعمل'}`);
        
        return {
            success: allFunctionsWorking && functionsInDisplay >= 36,
            functionsCount: Object.keys(functions36Results).length,
            displayFunctionsCount: functionsInDisplay,
            filesCount: filesInDisplay,
            totalContentSize: Object.values(functions36Results).reduce((total, content) => total + (content ? content.length : 0), 0)
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testAll36FunctionsContent()
    .then(result => {
        console.log('');
        console.log('🎉 انتهى اختبار الدوال الـ36!');
        console.log(`🏆 النتيجة النهائية: ${result.success ? 'نجح ✅' : 'فشل ❌'}`);
        if (result.error) {
            console.log(`❌ خطأ: ${result.error}`);
        }
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ خطأ فادح في الاختبار:', error);
        process.exit(1);
    });
