// 🧪 اختبار حقيقي لجميع الدوال الـ36 الموجودة فعلاً في النظام v4
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testRealAll36Functions() {
    console.log('🧪 بدء اختبار حقيقي لجميع الدوال الـ36 الموجودة فعلاً في النظام v4...');
    console.log('=' .repeat(100));
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // ثغرة اختبار
        const testVulnerability = {
            name: 'SQL Injection اختبار شامل',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://example.com/test.php?id=1',
            parameter: 'id',
            payload: "1' OR 1=1 --",
            description: 'ثغرة SQL Injection للاختبار الشامل',
            exploitation_confirmed: true,
            real_test_result: 'تم تأكيد الثغرة بنجاح'
        };

        // استخراج البيانات الحقيقية
        const realData = bugBounty.extractRealDataFromDiscoveredVulnerability ? 
            bugBounty.extractRealDataFromDiscoveredVulnerability(testVulnerability) : {};
        
        console.log('📊 البيانات الحقيقية المستخرجة:');
        console.log(`   📍 الموقع: ${realData.location}`);
        console.log(`   🎯 Payload: ${realData.payload}`);
        console.log(`   🔍 المعامل: ${realData.parameter}`);
        console.log('');
        
        // قائمة جميع الدوال الـ36 الموجودة فعلاً في النظام
        const all36Functions = [
            // الدوال الأساسية (1-6)
            'generateComprehensiveDetailsFromRealData',
            'extractRealDataFromDiscoveredVulnerability', 
            'generateDynamicImpactForAnyVulnerability',
            'generateRealExploitationStepsForVulnerabilityComprehensive',
            'generateDynamicRecommendationsForVulnerability',
            'generateComprehensiveRiskAnalysis',
            
            // الدوال المتقدمة (7-12)
            'generateComprehensiveVulnerabilityAnalysis',
            'generateDynamicSecurityImpactAnalysis',
            'generateRealTimeVulnerabilityAssessment',
            'generateAdvancedExploitationTechniques',
            'generateComprehensiveRemediationPlan',
            'generateDynamicRecommendationsForVulnerability',
            
            // الدوال التخصصية (13-18)
            'generateDynamicThreatModelingForVulnerability',
            'generateComprehensiveTestingDetails',
            'generateVisualChangesForVulnerability',
            'generatePersistentResultsForVulnerability',
            'generateAdvancedThreatIntelligence',
            'generateComprehensivePayloadAnalysis',
            
            // الدوال التصورية (19-24)
            'generateComprehensiveResponseAnalysis',
            'generateDynamicExploitationChain',
            'generateRealTimeSecurityMetrics',
            'captureRealTimeScreenshots',
            'analyzeVisualChangesComprehensive',
            'generateInteractiveReports',
            
            // الدوال التفاعلية (25-30)
            'displayRealTimeResults',
            'generateInteractiveDialogue',
            'generateComplianceReport',
            'generateForensicAnalysisReport',
            'generateAdvancedSecurityAnalysis',
            'generateRiskAssessmentMatrix',

            // الدوال المثابرة (31-36)
            'generateComplianceMapping',
            'generateBusinessImpactAnalysis',
            'generateSecurityControls',
            'generateAttackVectors',
            'generateForensicAnalysis',
            'generateVisualizationCharts'
        ];
        
        console.log(`🔥 اختبار جميع الدوال الـ${all36Functions.length} الموجودة فعلاً في النظام...`);
        console.log('=' .repeat(80));
        
        const functionsResults = {};
        let totalContentSize = 0;
        let workingFunctions = 0;
        let failedFunctions = 0;
        
        // اختبار كل دالة
        for (let i = 0; i < all36Functions.length; i++) {
            const functionName = all36Functions[i];
            const functionNumber = i + 1;
            
            console.log(`🔥 Function ${functionNumber}: ${functionName}`);
            
            try {
                if (typeof bugBounty[functionName] === 'function') {
                    const startTime = Date.now();
                    const result = await bugBounty[functionName](testVulnerability, realData);
                    const endTime = Date.now();
                    const executionTime = endTime - startTime;
                    
                    if (result && typeof result === 'string' && result.length > 0) {
                        functionsResults[`function${functionNumber}`] = {
                            name: functionName,
                            content: result,
                            size: result.length,
                            executionTime: executionTime,
                            status: 'success'
                        };
                        
                        totalContentSize += result.length;
                        workingFunctions++;
                        
                        console.log(`   ✅ نجح - حجم المحتوى: ${result.length} حرف - وقت التنفيذ: ${executionTime}ms`);
                        console.log(`   📝 المحتوى (أول 150 حرف): ${result.substring(0, 150)}...`);
                    } else {
                        functionsResults[`function${functionNumber}`] = {
                            name: functionName,
                            content: result,
                            size: 0,
                            executionTime: executionTime,
                            status: 'empty_result'
                        };
                        
                        failedFunctions++;
                        console.log(`   ⚠️ نتيجة فارغة - وقت التنفيذ: ${executionTime}ms`);
                    }
                } else {
                    functionsResults[`function${functionNumber}`] = {
                        name: functionName,
                        content: null,
                        size: 0,
                        executionTime: 0,
                        status: 'not_found'
                    };
                    
                    failedFunctions++;
                    console.log(`   ❌ الدالة غير موجودة`);
                }
            } catch (error) {
                functionsResults[`function${functionNumber}`] = {
                    name: functionName,
                    content: null,
                    size: 0,
                    executionTime: 0,
                    status: 'error',
                    error: error.message
                };
                
                failedFunctions++;
                console.log(`   ❌ خطأ: ${error.message}`);
            }
            
            console.log('');
        }
        
        console.log('🔍 اختبار عرض الدوال الـ36 في القالب:');
        console.log('-' .repeat(60));
        
        // اختبار عرض الدوال الـ36 في القالب
        const functionsDisplay = bugBounty.generateComprehensiveFunctionsDisplay();
        console.log(`📏 حجم عرض الدوال الـ36: ${functionsDisplay ? functionsDisplay.length : 0} حرف`);
        
        // عد الدوال في العرض
        const functionsInDisplay = (functionsDisplay.match(/Function \d+:/g) || []).length;
        console.log(`🔢 عدد الدوال في العرض: ${functionsInDisplay}/36`);
        
        console.log('🔍 اختبار الملفات الشاملة التفصيلية:');
        console.log('-' .repeat(60));

        // اختبار عرض الملفات الشاملة
        const filesDisplay = bugBounty.generateComprehensiveFilesDisplay();
        console.log(`📏 حجم عرض الملفات الشاملة: ${filesDisplay ? filesDisplay.length : 0} حرف`);

        // عد الملفات في العرض
        const filesInDisplay = (filesDisplay.match(/\.js|\.html|\.css|\.json/g) || []).length;
        console.log(`📁 عدد الملفات في العرض: ${filesInDisplay}`);

        console.log('🎨 اختبار التنسيق والتنظيم والترتيب والقالب الشامل:');
        console.log('-' .repeat(60));

        // اختبار إنشاء تقرير كامل مع التنسيق والتنظيم
        console.log('📋 إنشاء تقرير كامل لاختبار التنسيق والتنظيم...');
        const fullReport = await bugBounty.generateComprehensiveReport({
            url: 'https://example.com/test',
            vulnerabilities: [testVulnerability],
            total_vulnerabilities: 1
        });

        console.log(`📏 حجم التقرير الكامل: ${fullReport ? fullReport.length : 0} حرف`);

        // فحص التنسيق والتنظيم
        const formatChecks = {
            hasCSS: fullReport.includes('<style>') || fullReport.includes('style='),
            hasGradients: fullReport.includes('linear-gradient'),
            hasBorders: fullReport.includes('border-radius'),
            hasColors: fullReport.includes('color:'),
            hasSections: fullReport.includes('section'),
            hasHeaders: fullReport.includes('<h1>') || fullReport.includes('<h2>'),
            hasStructure: fullReport.includes('<div'),
            hasRTL: fullReport.includes('dir="rtl"') || fullReport.includes('direction: rtl'),
            hasArabicFont: fullReport.includes('font-family'),
            hasResponsive: fullReport.includes('responsive') || fullReport.includes('@media')
        };

        console.log('🎨 فحص التنسيق:');
        Object.entries(formatChecks).forEach(([key, value]) => {
            const checkName = {
                hasCSS: 'وجود CSS',
                hasGradients: 'التدرجات اللونية',
                hasBorders: 'الحدود المدورة',
                hasColors: 'الألوان',
                hasSections: 'الأقسام',
                hasHeaders: 'العناوين',
                hasStructure: 'البنية HTML',
                hasRTL: 'دعم العربية RTL',
                hasArabicFont: 'خطوط عربية',
                hasResponsive: 'التصميم المتجاوب'
            }[key] || key;
            console.log(`   ${checkName}: ${value ? '✅ موجود' : '❌ مفقود'}`);
        });

        // فحص ترتيب الأقسام
        const sectionOrder = [
            'الملخص التنفيذي',
            'تحليل التأثير',
            'الثغرات المكتشفة',
            'التغيرات البصرية',
            'الحوار التفاعلي',
            'التوصيات'
        ];

        console.log('📋 فحص ترتيب الأقسام:');
        let lastIndex = -1;
        let orderCorrect = true;
        sectionOrder.forEach((section, index) => {
            const currentIndex = fullReport.indexOf(section);
            if (currentIndex > lastIndex) {
                console.log(`   ${index + 1}. ${section}: ✅ في الترتيب الصحيح`);
                lastIndex = currentIndex;
            } else {
                console.log(`   ${index + 1}. ${section}: ❌ ترتيب خاطئ أو مفقود`);
                orderCorrect = false;
            }
        });

        // فحص الإصلاحات المطبقة
        console.log('🔧 فحص الإصلاحات المطبقة:');
        const fixChecks = {
            noInlineStyles: !fullReport.includes('style="') || fullReport.includes('<style>'),
            noContentDuplication: !fullReport.includes('تكرار المحتوى'),
            properSectionStructure: fullReport.includes('.section.summary') || fullReport.includes('class="section'),
            cleanHTML: !fullReport.includes('[object Object]') && !fullReport.includes('undefined'),
            noOverlap: !fullReport.includes('تداخل المحتوى'),
            properEncoding: !fullReport.includes('�') && fullReport.includes('charset=UTF-8')
        };

        Object.entries(fixChecks).forEach(([key, value]) => {
            const fixName = {
                noInlineStyles: 'إزالة الأنماط المضمنة',
                noContentDuplication: 'عدم تكرار المحتوى',
                properSectionStructure: 'بنية الأقسام الصحيحة',
                cleanHTML: 'HTML نظيف',
                noOverlap: 'عدم تداخل المحتوى',
                properEncoding: 'الترميز الصحيح'
            }[key] || key;
            console.log(`   ${fixName}: ${value ? '✅ مطبق' : '❌ غير مطبق'}`);
        });

        // اختبار التقرير المنفصل مع التنسيق
        console.log('📄 اختبار التقرير المنفصل مع التنسيق...');
        const separateReport = await bugBounty.formatSinglePageReport({
            page_name: 'اختبار التنسيق الشامل',
            page_url: 'https://example.com/test',
            vulnerabilities: [testVulnerability],
            interactive_dialogues: [{
                title: 'حوار تفاعلي للاختبار',
                content: 'محتوى الحوار التفاعلي'
            }],
            screenshots: [{
                title: 'لقطة شاشة الاختبار',
                url: 'test_screenshot.png',
                alt: 'لقطة شاشة للاختبار'
            }]
        });

        console.log(`📏 حجم التقرير المنفصل: ${separateReport ? separateReport.length : 0} حرف`);

        // فحص استخدام القالب الشامل
        const templateChecks = {
            usesComprehensiveTemplate: separateReport.includes('القالب الشامل') || separateReport.includes('comprehensive'),
            hasAllSections: separateReport.includes('summary') && separateReport.includes('vulnerabilities'),
            hasProperStructure: separateReport.includes('<!DOCTYPE html>'),
            hasMetaTags: separateReport.includes('<meta'),
            hasTitle: separateReport.includes('<title>'),
            hasBodyStructure: separateReport.includes('<body')
        };

        console.log('📋 فحص القالب الشامل:');
        Object.entries(templateChecks).forEach(([key, value]) => {
            const templateName = {
                usesComprehensiveTemplate: 'استخدام القالب الشامل',
                hasAllSections: 'جميع الأقسام موجودة',
                hasProperStructure: 'بنية HTML صحيحة',
                hasMetaTags: 'Meta Tags',
                hasTitle: 'عنوان الصفحة',
                hasBodyStructure: 'بنية Body'
            }[key] || key;
            console.log(`   ${templateName}: ${value ? '✅ موجود' : '❌ مفقود'}`);
        });

        // حفظ التقارير للفحص اليدوي
        const fs = require('fs');
        fs.writeFileSync('test_full_report_formatting.html', fullReport, 'utf8');
        fs.writeFileSync('test_separate_report_formatting.html', separateReport, 'utf8');
        console.log('💾 تم حفظ التقارير للفحص اليدوي:');
        console.log('   📄 test_full_report_formatting.html');
        console.log('   📄 test_separate_report_formatting.html');
        
        console.log('📁 اختبار محتوى الملفات الشاملة بالتفصيل:');
        console.log('-' .repeat(60));

        // فحص محتوى الملفات الشاملة
        const fileContentChecks = {
            hasBugBountyCore: filesDisplay.includes('BugBountyCore.js'),
            hasReportTemplate: filesDisplay.includes('report_template.html'),
            hasStyleCSS: filesDisplay.includes('style.css'),
            hasConfigFiles: filesDisplay.includes('.json'),
            hasModuleFiles: filesDisplay.includes('modules'),
            hasAssetFiles: filesDisplay.includes('assets'),
            hasDetailedDescriptions: filesDisplay.includes('وصف مفصل'),
            hasFileSizes: filesDisplay.includes('KB') || filesDisplay.includes('حجم'),
            hasFileTypes: filesDisplay.includes('نوع الملف'),
            hasFileStructure: filesDisplay.includes('بنية الملف')
        };

        console.log('📂 فحص محتوى الملفات:');
        Object.entries(fileContentChecks).forEach(([key, value]) => {
            const fileName = {
                hasBugBountyCore: 'BugBountyCore.js',
                hasReportTemplate: 'report_template.html',
                hasStyleCSS: 'style.css',
                hasConfigFiles: 'ملفات JSON',
                hasModuleFiles: 'ملفات الوحدات',
                hasAssetFiles: 'ملفات الأصول',
                hasDetailedDescriptions: 'أوصاف مفصلة',
                hasFileSizes: 'أحجام الملفات',
                hasFileTypes: 'أنواع الملفات',
                hasFileStructure: 'بنية الملفات'
            }[key] || key;
            console.log(`   ${fileName}: ${value ? '✅ موجود' : '❌ مفقود'}`);
        });

        // اختبار تكامل الدوال مع الملفات
        console.log('🔗 اختبار تكامل الدوال مع الملفات:');
        const integrationChecks = {
            functionsUseFiles: functionsDisplay.includes('استخدام الملفات'),
            filesUsedByFunctions: filesDisplay.includes('مستخدم بواسطة الدوال'),
            dynamicContent: functionsDisplay.includes('ديناميكي') && filesDisplay.includes('ديناميكي'),
            realTimeProcessing: functionsDisplay.includes('الوقت الفعلي') && filesDisplay.includes('معالجة'),
            comprehensiveIntegration: functionsDisplay.includes('شامل') && filesDisplay.includes('شامل')
        };

        Object.entries(integrationChecks).forEach(([key, value]) => {
            const integrationName = {
                functionsUseFiles: 'الدوال تستخدم الملفات',
                filesUsedByFunctions: 'الملفات مستخدمة بواسطة الدوال',
                dynamicContent: 'المحتوى الديناميكي',
                realTimeProcessing: 'المعالجة في الوقت الفعلي',
                comprehensiveIntegration: 'التكامل الشامل'
            }[key] || key;
            console.log(`   ${integrationName}: ${value ? '✅ متكامل' : '❌ غير متكامل'}`);
        });

        // حساب النتائج الشاملة
        const formatScore = Object.values(formatChecks).filter(Boolean).length / Object.keys(formatChecks).length * 100;
        const orderScore = orderCorrect ? 100 : 0;
        const fixScore = Object.values(fixChecks).filter(Boolean).length / Object.keys(fixChecks).length * 100;
        const templateScore = Object.values(templateChecks).filter(Boolean).length / Object.keys(templateChecks).length * 100;
        const fileContentScore = Object.values(fileContentChecks).filter(Boolean).length / Object.keys(fileContentChecks).length * 100;
        const integrationScore = Object.values(integrationChecks).filter(Boolean).length / Object.keys(integrationChecks).length * 100;

        console.log('');
        console.log('📊 نتائج التقييم الشامل:');
        console.log('-' .repeat(60));
        console.log(`🎨 التنسيق: ${formatScore.toFixed(1)}%`);
        console.log(`📋 الترتيب: ${orderScore.toFixed(1)}%`);
        console.log(`🔧 الإصلاحات: ${fixScore.toFixed(1)}%`);
        console.log(`📄 القالب: ${templateScore.toFixed(1)}%`);
        console.log(`📁 محتوى الملفات: ${fileContentScore.toFixed(1)}%`);
        console.log(`🔗 التكامل: ${integrationScore.toFixed(1)}%`);

        const overallQualityScore = (formatScore + orderScore + fixScore + templateScore + fileContentScore + integrationScore) / 6;
        console.log(`🏆 النتيجة الإجمالية للجودة: ${overallQualityScore.toFixed(1)}%`);

        // حفظ النتائج التفصيلية
        const detailedResults = {
            timestamp: new Date().toISOString(),
            vulnerability: testVulnerability,
            realData: realData,
            totalFunctions: all36Functions.length,
            workingFunctions: workingFunctions,
            failedFunctions: failedFunctions,
            totalContentSize: totalContentSize,
            functionsResults: functionsResults,
            functionsDisplay: {
                content: functionsDisplay,
                size: functionsDisplay ? functionsDisplay.length : 0,
                functionsCount: functionsInDisplay
            },
            filesDisplay: {
                content: filesDisplay,
                size: filesDisplay ? filesDisplay.length : 0,
                filesCount: filesInDisplay
            },
            qualityAssessment: {
                formatScore: formatScore,
                orderScore: orderScore,
                fixScore: fixScore,
                templateScore: templateScore,
                fileContentScore: fileContentScore,
                integrationScore: integrationScore,
                overallQualityScore: overallQualityScore
            },
            formatChecks: formatChecks,
            fixChecks: fixChecks,
            templateChecks: templateChecks,
            fileContentChecks: fileContentChecks,
            integrationChecks: integrationChecks,
            fullReportSize: fullReport ? fullReport.length : 0,
            separateReportSize: separateReport ? separateReport.length : 0
        };
        
        // حفظ النتائج
        fs.writeFileSync('test_real_36_functions_results.json', JSON.stringify(detailedResults, null, 2), 'utf8');
        console.log('💾 تم حفظ النتائج التفصيلية: test_real_36_functions_results.json');
        
        // إنشاء تقرير HTML شامل محسن
        const htmlReport = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار شامل للدوال الـ36 والتنسيق والقالب</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .function-result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .function-result.success { border-color: #28a745; background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%); }
        .function-result.failed { border-color: #dc3545; background: linear-gradient(135deg, #fff8f8 0%, #ffe6e6 100%); }
        .function-title { color: #007bff; font-weight: bold; margin-bottom: 10px; font-size: 1.1em; }
        .content-preview { background: #f8f9fa; padding: 12px; margin: 10px 0; border-radius: 5px; font-size: 0.9em; border-left: 4px solid #007bff; }
        .stats { background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%); padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #6c757d; }
        .quality-stats { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 20px; border-radius: 8px; margin: 20px 0; border: 2px solid #28a745; }
        .size-info { color: #666; font-size: 0.9em; margin: 5px 0; }
        .score { font-size: 1.2em; font-weight: bold; margin: 10px 0; }
        .score.excellent { color: #28a745; }
        .score.good { color: #ffc107; }
        .score.poor { color: #dc3545; }
        .section-header { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center; }
        .check-item { margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; }
        .check-item.pass { background: #d4edda; border-left: 4px solid #28a745; }
        .check-item.fail { background: #f8d7da; border-left: 4px solid #dc3545; }
        .tabs { display: flex; margin: 20px 0; }
        .tab { padding: 10px 20px; background: #e9ecef; border: 1px solid #dee2e6; cursor: pointer; border-radius: 5px 5px 0 0; margin-left: 5px; }
        .tab.active { background: #007bff; color: white; }
        .tab-content { border: 1px solid #dee2e6; padding: 20px; border-radius: 0 5px 5px 5px; background: white; }
    </style>
    <script>
        function showTab(tabName) {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.style.display = 'none');
            document.querySelector('[data-tab="' + tabName + '"]').classList.add('active');
            document.getElementById(tabName).style.display = 'block';
        }
    </script>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #007bff; margin-bottom: 30px;">🧪 اختبار شامل للدوال الـ36 والتنسيق والقالب الشامل</h1>

        <div class="stats">
            <h2>📊 إحصائيات الاختبار الأساسية:</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div><strong>إجمالي الدوال:</strong> ${all36Functions.length}/36</div>
                <div><strong>الدوال العاملة:</strong> ${workingFunctions} ✅</div>
                <div><strong>الدوال الفاشلة:</strong> ${failedFunctions} ❌</div>
                <div><strong>إجمالي حجم المحتوى:</strong> ${(totalContentSize / 1024).toFixed(2)} KB</div>
                <div><strong>الدوال في العرض:</strong> ${functionsInDisplay}/36</div>
                <div><strong>الملفات في العرض:</strong> ${filesInDisplay}</div>
                <div><strong>معدل النجاح:</strong> ${((workingFunctions / all36Functions.length) * 100).toFixed(1)}%</div>
                <div><strong>حجم التقرير الكامل:</strong> ${fullReport ? (fullReport.length / 1024).toFixed(2) : 0} KB</div>
            </div>
        </div>

        <div class="quality-stats">
            <h2>🏆 تقييم الجودة الشامل:</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div class="score ${formatScore >= 80 ? 'excellent' : formatScore >= 60 ? 'good' : 'poor'}">🎨 التنسيق: ${formatScore.toFixed(1)}%</div>
                <div class="score ${orderScore >= 80 ? 'excellent' : orderScore >= 60 ? 'good' : 'poor'}">📋 الترتيب: ${orderScore.toFixed(1)}%</div>
                <div class="score ${fixScore >= 80 ? 'excellent' : fixScore >= 60 ? 'good' : 'poor'}">🔧 الإصلاحات: ${fixScore.toFixed(1)}%</div>
                <div class="score ${templateScore >= 80 ? 'excellent' : templateScore >= 60 ? 'good' : 'poor'}">📄 القالب: ${templateScore.toFixed(1)}%</div>
                <div class="score ${fileContentScore >= 80 ? 'excellent' : fileContentScore >= 60 ? 'good' : 'poor'}">📁 محتوى الملفات: ${fileContentScore.toFixed(1)}%</div>
                <div class="score ${integrationScore >= 80 ? 'excellent' : integrationScore >= 60 ? 'good' : 'poor'}">🔗 التكامل: ${integrationScore.toFixed(1)}%</div>
            </div>
            <div class="score ${overallQualityScore >= 80 ? 'excellent' : overallQualityScore >= 60 ? 'good' : 'poor'}" style="text-align: center; font-size: 1.5em; margin-top: 20px;">
                🏆 النتيجة الإجمالية للجودة: ${overallQualityScore.toFixed(1)}%
            </div>
        </div>

        <div class="tabs">
            <div class="tab active" data-tab="functions" onclick="showTab('functions')">الدوال</div>
            <div class="tab" data-tab="formatting" onclick="showTab('formatting')">التنسيق</div>
            <div class="tab" data-tab="template" onclick="showTab('template')">القالب</div>
            <div class="tab" data-tab="files" onclick="showTab('files')">الملفات</div>
            <div class="tab" data-tab="integration" onclick="showTab('integration')">التكامل</div>
        </div>
    
    <h2>📋 نتائج الدوال:</h2>
    ${Object.entries(functionsResults).map(([key, result]) => `
        <div class="function-result ${result.status === 'success' ? 'success' : 'failed'}">
            <div class="function-title">${key}: ${result.name}</div>
            <div class="size-info">
                الحالة: ${result.status === 'success' ? '✅ نجح' : result.status === 'empty_result' ? '⚠️ نتيجة فارغة' : result.status === 'not_found' ? '❌ غير موجود' : '❌ خطأ'} | 
                حجم المحتوى: ${result.size} حرف | 
                وقت التنفيذ: ${result.executionTime}ms
                ${result.error ? ` | خطأ: ${result.error}` : ''}
            </div>
            ${result.content && result.content.length > 0 ? `<div class="content-preview">${result.content.substring(0, 300)}${result.content.length > 300 ? '...' : ''}</div>` : ''}
        </div>
    `).join('')}
    
    <h2>📂 عرض الدوال الـ36:</h2>
    <div class="size-info">حجم العرض: ${functionsDisplay ? functionsDisplay.length : 0} حرف | عدد الدوال: ${functionsInDisplay}/36</div>
    <div class="content-preview">${functionsDisplay || 'غير متاح'}</div>
    
    <h2>📁 عرض الملفات الشاملة:</h2>
    <div class="size-info">حجم العرض: ${filesDisplay ? filesDisplay.length : 0} حرف | عدد الملفات: ${filesInDisplay}</div>
    <div class="content-preview">${filesDisplay || 'غير متاح'}</div>
</body>
</html>`;
        
        fs.writeFileSync('test_real_36_functions_report.html', htmlReport, 'utf8');
        console.log('💾 تم حفظ التقرير HTML: test_real_36_functions_report.html');
        
        console.log('');
        console.log('🎯 ملخص النتائج النهائية:');
        console.log('=' .repeat(80));
        console.log(`📊 إجمالي الدوال المختبرة: ${all36Functions.length}/36`);
        console.log(`✅ الدوال العاملة: ${workingFunctions}`);
        console.log(`❌ الدوال الفاشلة: ${failedFunctions}`);
        console.log(`📏 إجمالي حجم المحتوى: ${(totalContentSize / 1024).toFixed(2)} KB`);
        console.log(`🔢 الدوال في العرض: ${functionsInDisplay}/36`);
        console.log(`📁 الملفات في العرض: ${filesInDisplay}`);
        console.log(`📈 معدل النجاح: ${((workingFunctions / all36Functions.length) * 100).toFixed(1)}%`);
        
        const allFunctionsWorking = workingFunctions === all36Functions.length;
        const displayComplete = functionsInDisplay >= 36;
        
        console.log(`🏆 حالة الدوال: ${allFunctionsWorking ? '✅ جميع الدوال تعمل' : `❌ ${failedFunctions} دوال لا تعمل`}`);
        console.log(`🏆 حالة العرض: ${displayComplete ? '✅ العرض مكتمل' : '❌ العرض ناقص'}`);
        
        return {
            success: allFunctionsWorking && displayComplete,
            totalFunctions: all36Functions.length,
            workingFunctions: workingFunctions,
            failedFunctions: failedFunctions,
            totalContentSize: totalContentSize,
            displayFunctionsCount: functionsInDisplay,
            filesCount: filesInDisplay,
            successRate: (workingFunctions / all36Functions.length) * 100
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testRealAll36Functions()
    .then(result => {
        console.log('');
        console.log('🎉 انتهى اختبار جميع الدوال الـ36 الحقيقية!');
        console.log(`🏆 النتيجة النهائية: ${result.success ? 'نجح ✅' : 'فشل ❌'}`);
        if (result.successRate) {
            console.log(`📈 معدل النجاح: ${result.successRate.toFixed(1)}%`);
        }
        if (result.error) {
            console.log(`❌ خطأ: ${result.error}`);
        }
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ خطأ فادح في الاختبار:', error);
        process.exit(1);
    });
