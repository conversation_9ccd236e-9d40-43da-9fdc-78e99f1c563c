// 🧪 اختبار حقيقي لجميع الدوال الـ36 الموجودة فعلاً في النظام v4
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testRealAll36Functions() {
    console.log('🧪 بدء اختبار حقيقي لجميع الدوال الـ36 الموجودة فعلاً في النظام v4...');
    console.log('=' .repeat(100));
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // ثغرة اختبار
        const testVulnerability = {
            name: 'SQL Injection اختبار شامل',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://example.com/test.php?id=1',
            parameter: 'id',
            payload: "1' OR 1=1 --",
            description: 'ثغرة SQL Injection للاختبار الشامل',
            exploitation_confirmed: true,
            real_test_result: 'تم تأكيد الثغرة بنجاح'
        };

        // استخراج البيانات الحقيقية
        const realData = bugBounty.extractRealDataFromDiscoveredVulnerability ? 
            bugBounty.extractRealDataFromDiscoveredVulnerability(testVulnerability) : {};
        
        console.log('📊 البيانات الحقيقية المستخرجة:');
        console.log(`   📍 الموقع: ${realData.location}`);
        console.log(`   🎯 Payload: ${realData.payload}`);
        console.log(`   🔍 المعامل: ${realData.parameter}`);
        console.log('');
        
        // قائمة جميع الدوال الـ36 الموجودة فعلاً في النظام
        const all36Functions = [
            // الدوال الأساسية (1-6)
            'generateComprehensiveDetailsFromRealData',
            'extractRealDataFromDiscoveredVulnerability', 
            'generateDynamicImpactForAnyVulnerability',
            'generateRealExploitationStepsForVulnerabilityComprehensive',
            'generateDynamicRecommendationsForVulnerability',
            'generateComprehensiveRiskAnalysis',
            
            // الدوال المتقدمة (7-12)
            'generateComprehensiveVulnerabilityAnalysis',
            'generateDynamicSecurityImpactAnalysis',
            'generateRealTimeVulnerabilityAssessment',
            'generateAdvancedExploitationTechniques',
            'generateComprehensiveRemediationPlan',
            'generateDynamicRecommendationsForVulnerability',
            
            // الدوال التخصصية (13-18)
            'generateDynamicThreatModelingForVulnerability',
            'generateComprehensiveTestingDetails',
            'generateVisualChangesForVulnerability',
            'generatePersistentResultsForVulnerability',
            'generateAdvancedThreatIntelligence',
            'generateComprehensivePayloadAnalysis',
            
            // الدوال التصورية (19-24)
            'generateComprehensiveResponseAnalysis',
            'generateDynamicExploitationChain',
            'generateRealTimeSecurityMetrics',
            'captureRealTimeScreenshots',
            'analyzeVisualChangesComprehensive',
            'generateInteractiveReports',
            
            // الدوال التفاعلية (25-30)
            'displayRealTimeResults',
            'generateInteractiveDialogue',
            'generateComplianceReport',
            'generateForensicAnalysisReport',
            'generateAdvancedSecurityAnalysis',
            'generateRiskAssessmentMatrix',

            // الدوال المثابرة (31-36)
            'generateComplianceMapping',
            'generateBusinessImpactAnalysis',
            'generateSecurityControls',
            'generateAttackVectors',
            'generateForensicAnalysis',
            'generateVisualizationCharts'
        ];
        
        console.log(`🔥 اختبار جميع الدوال الـ${all36Functions.length} الموجودة فعلاً في النظام...`);
        console.log('=' .repeat(80));
        
        const functionsResults = {};
        let totalContentSize = 0;
        let workingFunctions = 0;
        let failedFunctions = 0;
        
        // اختبار كل دالة
        for (let i = 0; i < all36Functions.length; i++) {
            const functionName = all36Functions[i];
            const functionNumber = i + 1;
            
            console.log(`🔥 Function ${functionNumber}: ${functionName}`);
            
            try {
                if (typeof bugBounty[functionName] === 'function') {
                    const startTime = Date.now();
                    const result = await bugBounty[functionName](testVulnerability, realData);
                    const endTime = Date.now();
                    const executionTime = endTime - startTime;
                    
                    if (result && typeof result === 'string' && result.length > 0) {
                        functionsResults[`function${functionNumber}`] = {
                            name: functionName,
                            content: result,
                            size: result.length,
                            executionTime: executionTime,
                            status: 'success'
                        };
                        
                        totalContentSize += result.length;
                        workingFunctions++;
                        
                        console.log(`   ✅ نجح - حجم المحتوى: ${result.length} حرف - وقت التنفيذ: ${executionTime}ms`);
                        console.log(`   📝 المحتوى (أول 150 حرف): ${result.substring(0, 150)}...`);
                    } else {
                        functionsResults[`function${functionNumber}`] = {
                            name: functionName,
                            content: result,
                            size: 0,
                            executionTime: executionTime,
                            status: 'empty_result'
                        };
                        
                        failedFunctions++;
                        console.log(`   ⚠️ نتيجة فارغة - وقت التنفيذ: ${executionTime}ms`);
                    }
                } else {
                    functionsResults[`function${functionNumber}`] = {
                        name: functionName,
                        content: null,
                        size: 0,
                        executionTime: 0,
                        status: 'not_found'
                    };
                    
                    failedFunctions++;
                    console.log(`   ❌ الدالة غير موجودة`);
                }
            } catch (error) {
                functionsResults[`function${functionNumber}`] = {
                    name: functionName,
                    content: null,
                    size: 0,
                    executionTime: 0,
                    status: 'error',
                    error: error.message
                };
                
                failedFunctions++;
                console.log(`   ❌ خطأ: ${error.message}`);
            }
            
            console.log('');
        }
        
        console.log('🔍 اختبار عرض الدوال الـ36 في القالب:');
        console.log('-' .repeat(60));
        
        // اختبار عرض الدوال الـ36 في القالب
        const functionsDisplay = bugBounty.generateComprehensiveFunctionsDisplay();
        console.log(`📏 حجم عرض الدوال الـ36: ${functionsDisplay ? functionsDisplay.length : 0} حرف`);
        
        // عد الدوال في العرض
        const functionsInDisplay = (functionsDisplay.match(/Function \d+:/g) || []).length;
        console.log(`🔢 عدد الدوال في العرض: ${functionsInDisplay}/36`);
        
        console.log('🔍 اختبار الملفات الشاملة التفصيلية:');
        console.log('-' .repeat(60));
        
        // اختبار عرض الملفات الشاملة
        const filesDisplay = bugBounty.generateComprehensiveFilesDisplay();
        console.log(`📏 حجم عرض الملفات الشاملة: ${filesDisplay ? filesDisplay.length : 0} حرف`);
        
        // عد الملفات في العرض
        const filesInDisplay = (filesDisplay.match(/\.js|\.html|\.css|\.json/g) || []).length;
        console.log(`📁 عدد الملفات في العرض: ${filesInDisplay}`);
        
        // حفظ النتائج التفصيلية
        const detailedResults = {
            timestamp: new Date().toISOString(),
            vulnerability: testVulnerability,
            realData: realData,
            totalFunctions: all36Functions.length,
            workingFunctions: workingFunctions,
            failedFunctions: failedFunctions,
            totalContentSize: totalContentSize,
            functionsResults: functionsResults,
            functionsDisplay: {
                content: functionsDisplay,
                size: functionsDisplay ? functionsDisplay.length : 0,
                functionsCount: functionsInDisplay
            },
            filesDisplay: {
                content: filesDisplay,
                size: filesDisplay ? filesDisplay.length : 0,
                filesCount: filesInDisplay
            }
        };
        
        // حفظ النتائج
        fs.writeFileSync('test_real_36_functions_results.json', JSON.stringify(detailedResults, null, 2), 'utf8');
        console.log('💾 تم حفظ النتائج التفصيلية: test_real_36_functions_results.json');
        
        // إنشاء تقرير HTML شامل
        const htmlReport = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار جميع الدوال الـ36 الحقيقية</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .function-result { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .function-result.success { border-color: #28a745; background: #f8fff9; }
        .function-result.failed { border-color: #dc3545; background: #fff8f8; }
        .function-title { color: #007bff; font-weight: bold; margin-bottom: 10px; }
        .content-preview { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-size: 0.9em; }
        .stats { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .size-info { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>🧪 نتائج اختبار جميع الدوال الـ36 الحقيقية</h1>
    
    <div class="stats">
        <h2>📊 إحصائيات الاختبار:</h2>
        <p><strong>إجمالي الدوال:</strong> ${all36Functions.length}</p>
        <p><strong>الدوال العاملة:</strong> ${workingFunctions} ✅</p>
        <p><strong>الدوال الفاشلة:</strong> ${failedFunctions} ❌</p>
        <p><strong>إجمالي حجم المحتوى:</strong> ${(totalContentSize / 1024).toFixed(2)} KB</p>
        <p><strong>الدوال في العرض:</strong> ${functionsInDisplay}/36</p>
        <p><strong>الملفات في العرض:</strong> ${filesInDisplay}</p>
        <p><strong>معدل النجاح:</strong> ${((workingFunctions / all36Functions.length) * 100).toFixed(1)}%</p>
    </div>
    
    <h2>📋 نتائج الدوال:</h2>
    ${Object.entries(functionsResults).map(([key, result]) => `
        <div class="function-result ${result.status === 'success' ? 'success' : 'failed'}">
            <div class="function-title">${key}: ${result.name}</div>
            <div class="size-info">
                الحالة: ${result.status === 'success' ? '✅ نجح' : result.status === 'empty_result' ? '⚠️ نتيجة فارغة' : result.status === 'not_found' ? '❌ غير موجود' : '❌ خطأ'} | 
                حجم المحتوى: ${result.size} حرف | 
                وقت التنفيذ: ${result.executionTime}ms
                ${result.error ? ` | خطأ: ${result.error}` : ''}
            </div>
            ${result.content && result.content.length > 0 ? `<div class="content-preview">${result.content.substring(0, 300)}${result.content.length > 300 ? '...' : ''}</div>` : ''}
        </div>
    `).join('')}
    
    <h2>📂 عرض الدوال الـ36:</h2>
    <div class="size-info">حجم العرض: ${functionsDisplay ? functionsDisplay.length : 0} حرف | عدد الدوال: ${functionsInDisplay}/36</div>
    <div class="content-preview">${functionsDisplay || 'غير متاح'}</div>
    
    <h2>📁 عرض الملفات الشاملة:</h2>
    <div class="size-info">حجم العرض: ${filesDisplay ? filesDisplay.length : 0} حرف | عدد الملفات: ${filesInDisplay}</div>
    <div class="content-preview">${filesDisplay || 'غير متاح'}</div>
</body>
</html>`;
        
        fs.writeFileSync('test_real_36_functions_report.html', htmlReport, 'utf8');
        console.log('💾 تم حفظ التقرير HTML: test_real_36_functions_report.html');
        
        console.log('');
        console.log('🎯 ملخص النتائج النهائية:');
        console.log('=' .repeat(80));
        console.log(`📊 إجمالي الدوال المختبرة: ${all36Functions.length}/36`);
        console.log(`✅ الدوال العاملة: ${workingFunctions}`);
        console.log(`❌ الدوال الفاشلة: ${failedFunctions}`);
        console.log(`📏 إجمالي حجم المحتوى: ${(totalContentSize / 1024).toFixed(2)} KB`);
        console.log(`🔢 الدوال في العرض: ${functionsInDisplay}/36`);
        console.log(`📁 الملفات في العرض: ${filesInDisplay}`);
        console.log(`📈 معدل النجاح: ${((workingFunctions / all36Functions.length) * 100).toFixed(1)}%`);
        
        const allFunctionsWorking = workingFunctions === all36Functions.length;
        const displayComplete = functionsInDisplay >= 36;
        
        console.log(`🏆 حالة الدوال: ${allFunctionsWorking ? '✅ جميع الدوال تعمل' : `❌ ${failedFunctions} دوال لا تعمل`}`);
        console.log(`🏆 حالة العرض: ${displayComplete ? '✅ العرض مكتمل' : '❌ العرض ناقص'}`);
        
        return {
            success: allFunctionsWorking && displayComplete,
            totalFunctions: all36Functions.length,
            workingFunctions: workingFunctions,
            failedFunctions: failedFunctions,
            totalContentSize: totalContentSize,
            displayFunctionsCount: functionsInDisplay,
            filesCount: filesInDisplay,
            successRate: (workingFunctions / all36Functions.length) * 100
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testRealAll36Functions()
    .then(result => {
        console.log('');
        console.log('🎉 انتهى اختبار جميع الدوال الـ36 الحقيقية!');
        console.log(`🏆 النتيجة النهائية: ${result.success ? 'نجح ✅' : 'فشل ❌'}`);
        if (result.successRate) {
            console.log(`📈 معدل النجاح: ${result.successRate.toFixed(1)}%`);
        }
        if (result.error) {
            console.log(`❌ خطأ: ${result.error}`);
        }
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ خطأ فادح في الاختبار:', error);
        process.exit(1);
    });
