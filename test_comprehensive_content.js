// 🧪 اختبار شامل لعرض المحتوى التفصيلي للدوال الـ36 والملفات الشاملة
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testComprehensiveContent() {
    console.log('🧪 بدء الاختبار الشامل للمحتوى التفصيلي...');
    console.log('=' .repeat(80));
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار شاملة مع ثغرات متنوعة
        const testVulnerabilities = [
            {
                name: 'SQL Injection في معامل البحث',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'https://example.com/search.php?q=test',
                parameter: 'q',
                payload: "' OR 1=1 --",
                description: 'ثغرة SQL Injection خطيرة تسمح بالوصول لقاعدة البيانات',
                exploitation_confirmed: true,
                real_test_result: 'تم تأكيد الثغرة - استجابة مختلفة'
            },
            {
                name: 'Cross-Site Scripting (XSS)',
                type: 'XSS',
                severity: 'High',
                url: 'https://example.com/comment.php',
                parameter: 'comment',
                payload: '<script>alert("XSS")</script>',
                description: 'ثغرة XSS تسمح بتنفيذ كود JavaScript',
                exploitation_confirmed: true,
                real_test_result: 'تم تنفيذ الكود بنجاح'
            },
            {
                name: 'Directory Traversal',
                type: 'Path Traversal',
                severity: 'Medium',
                url: 'https://example.com/file.php?path=../../../etc/passwd',
                parameter: 'path',
                payload: '../../../etc/passwd',
                description: 'ثغرة تسمح بالوصول للملفات الحساسة',
                exploitation_confirmed: false,
                real_test_result: 'محاولة فاشلة - تم حجب المسار'
            }
        ];

        console.log(`📊 اختبار ${testVulnerabilities.length} ثغرات متنوعة...`);
        
        // بيانات الصفحة للاختبار
        const pageData = {
            page_name: 'اختبار شامل للمحتوى التفصيلي',
            page_url: 'https://example.com/test',
            vulnerabilities: testVulnerabilities,
            interactive_dialogues: [
                {
                    title: 'حوار تفاعلي للاختبار',
                    content: 'محتوى الحوار التفاعلي للاختبار'
                }
            ],
            screenshots: [
                {
                    title: 'لقطة شاشة الاختبار',
                    url: 'test_screenshot.png',
                    alt: 'لقطة شاشة للاختبار'
                }
            ]
        };

        console.log('🔥 بدء اختبار التقرير الرئيسي...');
        console.log('-' .repeat(60));

        // اختبار التقرير الرئيسي
        const mainReport = await bugBounty.generateComprehensiveReport({
            url: 'https://example.com/test',
            vulnerabilities: testVulnerabilities,
            total_vulnerabilities: testVulnerabilities.length
        });

        // 💾 حفظ التقرير الرئيسي
        const fs = require('fs');
        const mainReportPath = 'test_main_report.html';
        fs.writeFileSync(mainReportPath, mainReport, 'utf8');
        console.log(`💾 تم حفظ التقرير الرئيسي: ${mainReportPath}`);

        console.log('🔥 بدء اختبار التقرير المنفصل...');
        console.log('-' .repeat(60));

        // اختبار التقرير المنفصل
        const separateReport = await bugBounty.formatSinglePageReport(pageData);

        // 💾 حفظ التقرير المنفصل
        const separateReportPath = 'test_separate_report.html';
        fs.writeFileSync(separateReportPath, separateReport, 'utf8');
        console.log(`💾 تم حفظ التقرير المنفصل: ${separateReportPath}`);
        
        // 🔍 اختبار إضافي - إنشاء تقرير مع ثغرات أكثر
        console.log('🔥 اختبار إضافي مع ثغرات أكثر...');
        const moreVulns = [...testVulnerabilities];
        for (let i = 4; i <= 10; i++) {
            moreVulns.push({
                name: `ثغرة اختبار ${i}`,
                type: 'Test Vulnerability',
                severity: i % 2 === 0 ? 'High' : 'Medium',
                url: `https://example.com/test${i}.php`,
                parameter: `param${i}`,
                payload: `test_payload_${i}`,
                description: `وصف تفصيلي للثغرة رقم ${i}`,
                exploitation_confirmed: i % 3 === 0,
                real_test_result: `نتيجة اختبار الثغرة ${i}`
            });
        }

        const extendedPageData = { ...pageData, vulnerabilities: moreVulns };
        const extendedReport = await bugBounty.formatSinglePageReport(extendedPageData);

        // 💾 حفظ التقرير الموسع
        const extendedReportPath = 'test_extended_report.html';
        fs.writeFileSync(extendedReportPath, extendedReport, 'utf8');
        console.log(`💾 تم حفظ التقرير الموسع (${moreVulns.length} ثغرات): ${extendedReportPath}`);

        console.log('🔍 تحليل نتائج الاختبار...');
        console.log('=' .repeat(80));
        
        // تحليل التقرير الرئيسي
        console.log('📊 تحليل التقرير الرئيسي:');
        const mainFunctionsCount = (mainReport.match(/Function \d+:/g) || []).length;
        const mainFilesCount = (mainReport.match(/\.js|\.html|\.css|\.json/g) || []).length;
        const mainHasFunctions = mainReport.includes('مجموعات الدوال الـ36');
        const mainHasFiles = mainReport.includes('الملفات الشاملة التفصيلية');
        const mainHasDetails = mainReport.includes('المحتوى المكتمل');
        
        console.log(`   📈 حجم التقرير: ${(mainReport.length / 1024).toFixed(2)} KB`);
        console.log(`   🔢 عدد الدوال المعروضة: ${mainFunctionsCount}/36`);
        console.log(`   📁 عدد الملفات المعروضة: ${mainFilesCount}`);
        console.log(`   ✅ وجود الدوال الـ36: ${mainHasFunctions ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود الملفات الشاملة: ${mainHasFiles ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود المحتوى المكتمل: ${mainHasDetails ? 'نعم' : 'لا'}`);
        
        console.log('');
        console.log('📊 تحليل التقرير المنفصل:');
        const separateFunctionsCount = (separateReport.match(/Function \d+:/g) || []).length;
        const separateFilesCount = (separateReport.match(/\.js|\.html|\.css|\.json/g) || []).length;
        const separateHasFunctions = separateReport.includes('مجموعات الدوال الـ36');
        const separateHasFiles = separateReport.includes('الملفات الشاملة التفصيلية');
        const separateHasDetails = separateReport.includes('المحتوى المكتمل');
        
        console.log(`   📈 حجم التقرير: ${(separateReport.length / 1024).toFixed(2)} KB`);
        console.log(`   🔢 عدد الدوال المعروضة: ${separateFunctionsCount}/36`);
        console.log(`   📁 عدد الملفات المعروضة: ${separateFilesCount}`);
        console.log(`   ✅ وجود الدوال الـ36: ${separateHasFunctions ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود الملفات الشاملة: ${separateHasFiles ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود المحتوى المكتمل: ${separateHasDetails ? 'نعم' : 'لا'}`);
        
        console.log('');
        console.log('🎯 نتائج الاختبار النهائية:');
        console.log('=' .repeat(80));
        
        // تقييم النتائج
        const mainSuccess = mainFunctionsCount >= 36 && mainHasFunctions && mainHasFiles && mainHasDetails;
        const separateSuccess = separateFunctionsCount >= 36 && separateHasFunctions && separateHasFiles && separateHasDetails;
        
        // تحليل التقرير الموسع
        console.log('');
        console.log('📊 تحليل التقرير الموسع:');
        const extendedFunctionsCount = (extendedReport.match(/Function \d+:/g) || []).length;
        const extendedFilesCount = (extendedReport.match(/\.js|\.html|\.css|\.json/g) || []).length;
        const extendedHasFunctions = extendedReport.includes('مجموعات الدوال الـ36');
        const extendedHasFiles = extendedReport.includes('الملفات الشاملة التفصيلية');
        const extendedHasDetails = extendedReport.includes('المحتوى المكتمل');

        console.log(`   📈 حجم التقرير: ${(extendedReport.length / 1024).toFixed(2)} KB`);
        console.log(`   🔢 عدد الدوال المعروضة: ${extendedFunctionsCount}/36`);
        console.log(`   📁 عدد الملفات المعروضة: ${extendedFilesCount}`);
        console.log(`   ✅ وجود الدوال الـ36: ${extendedHasFunctions ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود الملفات الشاملة: ${extendedHasFiles ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود المحتوى المكتمل: ${extendedHasDetails ? 'نعم' : 'لا'}`);

        console.log('');
        console.log('📁 الملفات المُنشأة:');
        console.log(`   📄 ${mainReportPath} - ${(fs.statSync(mainReportPath).size / 1024).toFixed(2)} KB`);
        console.log(`   📄 ${separateReportPath} - ${(fs.statSync(separateReportPath).size / 1024).toFixed(2)} KB`);
        console.log(`   📄 ${extendedReportPath} - ${(fs.statSync(extendedReportPath).size / 1024).toFixed(2)} KB`);

        const extendedSuccess = extendedFunctionsCount >= 36 && extendedHasFunctions && extendedHasFiles && extendedHasDetails;

        console.log(`🏆 التقرير الرئيسي: ${mainSuccess ? '✅ نجح' : '❌ فشل'}`);
        console.log(`🏆 التقرير المنفصل: ${separateSuccess ? '✅ نجح' : '❌ فشل'}`);
        console.log(`🏆 التقرير الموسع: ${extendedSuccess ? '✅ نجح' : '❌ فشل'}`);
        console.log(`🏆 النتيجة الإجمالية: ${mainSuccess && separateSuccess && extendedSuccess ? '✅ نجح بالكامل' : '❌ يحتاج إصلاح'}`);

        if (!mainSuccess || !separateSuccess || !extendedSuccess) {
            console.log('');
            console.log('⚠️ مشاكل مكتشفة:');
            if (mainFunctionsCount < 36) console.log(`   - التقرير الرئيسي: عدد الدوال ${mainFunctionsCount} أقل من 36`);
            if (separateFunctionsCount < 36) console.log(`   - التقرير المنفصل: عدد الدوال ${separateFunctionsCount} أقل من 36`);
            if (extendedFunctionsCount < 36) console.log(`   - التقرير الموسع: عدد الدوال ${extendedFunctionsCount} أقل من 36`);
            if (!mainHasFunctions) console.log(`   - التقرير الرئيسي: الدوال الـ36 مفقودة`);
            if (!separateHasFunctions) console.log(`   - التقرير المنفصل: الدوال الـ36 مفقودة`);
            if (!extendedHasFunctions) console.log(`   - التقرير الموسع: الدوال الـ36 مفقودة`);
            if (!mainHasFiles) console.log(`   - التقرير الرئيسي: الملفات الشاملة مفقودة`);
            if (!separateHasFiles) console.log(`   - التقرير المنفصل: الملفات الشاملة مفقودة`);
            if (!extendedHasFiles) console.log(`   - التقرير الموسع: الملفات الشاملة مفقودة`);
            if (!mainHasDetails) console.log(`   - التقرير الرئيسي: المحتوى المكتمل مفقود`);
            if (!separateHasDetails) console.log(`   - التقرير المنفصل: المحتوى المكتمل مفقود`);
            if (!extendedHasDetails) console.log(`   - التقرير الموسع: المحتوى المكتمل مفقود`);
        }
        
        // 🔍 تحليل مفصل للمحتوى
        console.log('');
        console.log('🔍 تحليل مفصل للمحتوى:');
        console.log('-' .repeat(60));

        // فحص الدوال الـ36 بالتفصيل
        const functionPatterns = [
            'generateComprehensiveDetailsFromRealData',
            'extractRealDataFromDiscoveredVulnerability',
            'generateDynamicImpactAnalysis',
            'calculateRealRiskAssessment',
            'generateRealExploitationStepsForVulnerabilityComprehensive',
            'collectComprehensiveEvidence'
        ];

        console.log('📊 فحص الدوال الأساسية:');
        functionPatterns.forEach((pattern, index) => {
            const inMain = mainReport.includes(pattern);
            const inSeparate = separateReport.includes(pattern);
            const inExtended = extendedReport.includes(pattern);
            console.log(`   Function ${index + 1}: ${pattern.substring(0, 30)}... - رئيسي:${inMain ? '✅' : '❌'} منفصل:${inSeparate ? '✅' : '❌'} موسع:${inExtended ? '✅' : '❌'}`);
        });

        // فحص الملفات الشاملة
        const filePatterns = ['.js', '.html', '.css', '.json', 'BugBountyCore.js', 'report_template.html'];
        console.log('');
        console.log('📁 فحص الملفات الشاملة:');
        filePatterns.forEach(pattern => {
            const mainCount = (mainReport.match(new RegExp(pattern.replace('.', '\\.'), 'g')) || []).length;
            const separateCount = (separateReport.match(new RegExp(pattern.replace('.', '\\.'), 'g')) || []).length;
            const extendedCount = (extendedReport.match(new RegExp(pattern.replace('.', '\\.'), 'g')) || []).length;
            console.log(`   ${pattern}: رئيسي:${mainCount} منفصل:${separateCount} موسع:${extendedCount}`);
        });

        // فحص المحتوى المكتمل
        const contentPatterns = ['المحتوى المكتمل', 'تفاصيل شاملة', 'تحليل شامل', 'محتوى مكتمل'];
        console.log('');
        console.log('📝 فحص المحتوى المكتمل:');
        contentPatterns.forEach(pattern => {
            const mainCount = (mainReport.match(new RegExp(pattern, 'g')) || []).length;
            const separateCount = (separateReport.match(new RegExp(pattern, 'g')) || []).length;
            const extendedCount = (extendedReport.match(new RegExp(pattern, 'g')) || []).length;
            console.log(`   "${pattern}": رئيسي:${mainCount} منفصل:${separateCount} موسع:${extendedCount}`);
        });

        return {
            mainSuccess,
            separateSuccess,
            extendedSuccess,
            overall: mainSuccess && separateSuccess && extendedSuccess,
            details: {
                main: { functions: mainFunctionsCount, files: mainFilesCount, size: mainReport.length },
                separate: { functions: separateFunctionsCount, files: separateFilesCount, size: separateReport.length },
                extended: { functions: extendedFunctionsCount, files: extendedFilesCount, size: extendedReport.length }
            },
            files: [mainReportPath, separateReportPath, extendedReportPath]
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { overall: false, error: error.message };
    }
}

// تشغيل الاختبار
testComprehensiveContent()
    .then(result => {
        console.log('');
        console.log('🎉 انتهى الاختبار الشامل!');
        console.log(`🏆 النتيجة النهائية: ${result.overall ? 'نجح ✅' : 'فشل ❌'}`);
        if (result.error) {
            console.log(`❌ خطأ: ${result.error}`);
        }
        process.exit(result.overall ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ خطأ فادح في الاختبار:', error);
        process.exit(1);
    });
