// 🧪 اختبار شامل لعرض المحتوى التفصيلي للدوال الـ36 والملفات الشاملة
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testComprehensiveContent() {
    console.log('🧪 بدء الاختبار الشامل للمحتوى التفصيلي...');
    console.log('=' .repeat(80));
    
    try {
        // إنشاء مثيل من النظام
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار شاملة مع ثغرات متنوعة
        const testVulnerabilities = [
            {
                name: 'SQL Injection في معامل البحث',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'https://example.com/search.php?q=test',
                parameter: 'q',
                payload: "' OR 1=1 --",
                description: 'ثغرة SQL Injection خطيرة تسمح بالوصول لقاعدة البيانات',
                exploitation_confirmed: true,
                real_test_result: 'تم تأكيد الثغرة - استجابة مختلفة'
            },
            {
                name: 'Cross-Site Scripting (XSS)',
                type: 'XSS',
                severity: 'High',
                url: 'https://example.com/comment.php',
                parameter: 'comment',
                payload: '<script>alert("XSS")</script>',
                description: 'ثغرة XSS تسمح بتنفيذ كود JavaScript',
                exploitation_confirmed: true,
                real_test_result: 'تم تنفيذ الكود بنجاح'
            },
            {
                name: 'Directory Traversal',
                type: 'Path Traversal',
                severity: 'Medium',
                url: 'https://example.com/file.php?path=../../../etc/passwd',
                parameter: 'path',
                payload: '../../../etc/passwd',
                description: 'ثغرة تسمح بالوصول للملفات الحساسة',
                exploitation_confirmed: false,
                real_test_result: 'محاولة فاشلة - تم حجب المسار'
            }
        ];

        console.log(`📊 اختبار ${testVulnerabilities.length} ثغرات متنوعة...`);
        
        // بيانات الصفحة للاختبار
        const pageData = {
            page_name: 'اختبار شامل للمحتوى التفصيلي',
            page_url: 'https://example.com/test',
            vulnerabilities: testVulnerabilities,
            interactive_dialogues: [
                {
                    title: 'حوار تفاعلي للاختبار',
                    content: 'محتوى الحوار التفاعلي للاختبار'
                }
            ],
            screenshots: [
                {
                    title: 'لقطة شاشة الاختبار',
                    url: 'test_screenshot.png',
                    alt: 'لقطة شاشة للاختبار'
                }
            ]
        };

        console.log('🔥 بدء اختبار التقرير الرئيسي...');
        console.log('-' .repeat(60));
        
        // اختبار التقرير الرئيسي
        const mainReport = await bugBounty.generateComprehensiveReport({
            url: 'https://example.com/test',
            vulnerabilities: testVulnerabilities,
            total_vulnerabilities: testVulnerabilities.length
        });
        
        console.log('🔥 بدء اختبار التقرير المنفصل...');
        console.log('-' .repeat(60));
        
        // اختبار التقرير المنفصل
        const separateReport = await bugBounty.formatSinglePageReport(pageData);
        
        console.log('🔍 تحليل نتائج الاختبار...');
        console.log('=' .repeat(80));
        
        // تحليل التقرير الرئيسي
        console.log('📊 تحليل التقرير الرئيسي:');
        const mainFunctionsCount = (mainReport.match(/Function \d+:/g) || []).length;
        const mainFilesCount = (mainReport.match(/\.js|\.html|\.css|\.json/g) || []).length;
        const mainHasFunctions = mainReport.includes('مجموعات الدوال الـ36');
        const mainHasFiles = mainReport.includes('الملفات الشاملة التفصيلية');
        const mainHasDetails = mainReport.includes('المحتوى المكتمل');
        
        console.log(`   📈 حجم التقرير: ${(mainReport.length / 1024).toFixed(2)} KB`);
        console.log(`   🔢 عدد الدوال المعروضة: ${mainFunctionsCount}/36`);
        console.log(`   📁 عدد الملفات المعروضة: ${mainFilesCount}`);
        console.log(`   ✅ وجود الدوال الـ36: ${mainHasFunctions ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود الملفات الشاملة: ${mainHasFiles ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود المحتوى المكتمل: ${mainHasDetails ? 'نعم' : 'لا'}`);
        
        console.log('');
        console.log('📊 تحليل التقرير المنفصل:');
        const separateFunctionsCount = (separateReport.match(/Function \d+:/g) || []).length;
        const separateFilesCount = (separateReport.match(/\.js|\.html|\.css|\.json/g) || []).length;
        const separateHasFunctions = separateReport.includes('مجموعات الدوال الـ36');
        const separateHasFiles = separateReport.includes('الملفات الشاملة التفصيلية');
        const separateHasDetails = separateReport.includes('المحتوى المكتمل');
        
        console.log(`   📈 حجم التقرير: ${(separateReport.length / 1024).toFixed(2)} KB`);
        console.log(`   🔢 عدد الدوال المعروضة: ${separateFunctionsCount}/36`);
        console.log(`   📁 عدد الملفات المعروضة: ${separateFilesCount}`);
        console.log(`   ✅ وجود الدوال الـ36: ${separateHasFunctions ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود الملفات الشاملة: ${separateHasFiles ? 'نعم' : 'لا'}`);
        console.log(`   ✅ وجود المحتوى المكتمل: ${separateHasDetails ? 'نعم' : 'لا'}`);
        
        console.log('');
        console.log('🎯 نتائج الاختبار النهائية:');
        console.log('=' .repeat(80));
        
        // تقييم النتائج
        const mainSuccess = mainFunctionsCount >= 36 && mainHasFunctions && mainHasFiles && mainHasDetails;
        const separateSuccess = separateFunctionsCount >= 36 && separateHasFunctions && separateHasFiles && separateHasDetails;
        
        console.log(`🏆 التقرير الرئيسي: ${mainSuccess ? '✅ نجح' : '❌ فشل'}`);
        console.log(`🏆 التقرير المنفصل: ${separateSuccess ? '✅ نجح' : '❌ فشل'}`);
        console.log(`🏆 النتيجة الإجمالية: ${mainSuccess && separateSuccess ? '✅ نجح بالكامل' : '❌ يحتاج إصلاح'}`);
        
        if (!mainSuccess || !separateSuccess) {
            console.log('');
            console.log('⚠️ مشاكل مكتشفة:');
            if (mainFunctionsCount < 36) console.log(`   - التقرير الرئيسي: عدد الدوال ${mainFunctionsCount} أقل من 36`);
            if (separateFunctionsCount < 36) console.log(`   - التقرير المنفصل: عدد الدوال ${separateFunctionsCount} أقل من 36`);
            if (!mainHasFunctions) console.log(`   - التقرير الرئيسي: الدوال الـ36 مفقودة`);
            if (!separateHasFunctions) console.log(`   - التقرير المنفصل: الدوال الـ36 مفقودة`);
            if (!mainHasFiles) console.log(`   - التقرير الرئيسي: الملفات الشاملة مفقودة`);
            if (!separateHasFiles) console.log(`   - التقرير المنفصل: الملفات الشاملة مفقودة`);
            if (!mainHasDetails) console.log(`   - التقرير الرئيسي: المحتوى المكتمل مفقود`);
            if (!separateHasDetails) console.log(`   - التقرير المنفصل: المحتوى المكتمل مفقود`);
        }
        
        return {
            mainSuccess,
            separateSuccess,
            overall: mainSuccess && separateSuccess,
            details: {
                main: { functions: mainFunctionsCount, files: mainFilesCount, size: mainReport.length },
                separate: { functions: separateFunctionsCount, files: separateFilesCount, size: separateReport.length }
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('❌ تفاصيل الخطأ:', error.stack);
        return { overall: false, error: error.message };
    }
}

// تشغيل الاختبار
testComprehensiveContent()
    .then(result => {
        console.log('');
        console.log('🎉 انتهى الاختبار الشامل!');
        console.log(`🏆 النتيجة النهائية: ${result.overall ? 'نجح ✅' : 'فشل ❌'}`);
        if (result.error) {
            console.log(`❌ خطأ: ${result.error}`);
        }
        process.exit(result.overall ? 0 : 1);
    })
    .catch(error => {
        console.error('❌ خطأ فادح في الاختبار:', error);
        process.exit(1);
    });
