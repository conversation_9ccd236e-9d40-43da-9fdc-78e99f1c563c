<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="gradafter" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#f44336;stop-opacity:0.3" />
                </linearGradient>
                <filter id="shadow">
                    <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
                </filter>
            </defs>

            <!-- Background -->
            <rect width="800" height="600" fill="url(#gradafter)"/>

            <!-- Main Container -->
            <rect x="40" y="40" width="720" height="520" fill="white" rx="15" stroke="#f44336" stroke-width="3" filter="url(#shadow)"/>

            <!-- Header -->
            <rect x="50" y="50" width="700" height="80" fill="#f44336" rx="10"/>
            <text x="400" y="85" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="white">
                🚨 بعد الاستغلال
            </text>
            <text x="400" y="110" text-anchor="middle" font-family="Arial" font-size="16" fill="white">
                Bug Bounty v4.0 - Real Evidence
            </text>

            <!-- Vulnerability Info -->
            <rect x="70" y="150" width="660" height="120" fill="#f8f9fa" rx="8" stroke="#dee2e6" stroke-width="1"/>
            <text x="400" y="180" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#333">
                SQL Injection في نموذج تسجيل الدخول
            </text>
            <text x="400" y="210" text-anchor="middle" font-family="Arial" font-size="16" fill="#666">
                نوع الثغرة: SQL Injection
            </text>
            <text x="400" y="240" text-anchor="middle" font-family="Arial" font-size="14" fill="#888">
                الحالة: تم الاستغلال
            </text>

            <!-- Evidence Section -->
            <rect x="70" y="290" width="660" height="180" fill="#ffffff" rx="8" stroke="#f44336" stroke-width="2"/>
            <text x="400" y="320" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#f44336">
                🔍 دليل الاستغلال
            </text>

            <!-- Mock Browser Window -->
            <rect x="100" y="340" width="600" height="100" fill="#f1f3f4" rx="5" stroke="#dadce0" stroke-width="1"/>
            <rect x="100" y="340" width="600" height="25" fill="#e8eaed" rx="5 5 0 0"/>
            <circle cx="115" cy="352" r="4" fill="#ff5f56"/>
            <circle cx="135" cy="352" r="4" fill="#ffbd2e"/>
            <circle cx="155" cy="352" r="4" fill="#27ca3f"/>

            <!-- URL Bar -->
            <rect x="180" y="347" width="400" height="12" fill="white" rx="6"/>
            <text x="185" y="356" font-family="Arial" font-size="10" fill="#666">
                https://target-site.com/vulnerable-page
            </text>

            <!-- Content Area -->
            <text x="400" y="385" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">
                تم نجاح الاستغلال
            </text>
            <text x="400" y="405" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">
                تم تأكيد وجود الثغرة بنجاح
            </text>

            <!-- Footer -->
            <rect x="70" y="490" width="660" height="50" fill="#e9ecef" rx="8"/>
            <text x="400" y="510" text-anchor="middle" font-family="Arial" font-size="12" fill="#495057">
                📅 التوقيت: ١٨‏/٧‏/٢٠٢٥، ٤:٠٢:٤٣ ص
            </text>
            <text x="400" y="525" text-anchor="middle" font-family="Arial" font-size="10" fill="#6c757d">
                تم إنشاؤها بواسطة Bug Bounty System v4.0
            </text>
        </svg>