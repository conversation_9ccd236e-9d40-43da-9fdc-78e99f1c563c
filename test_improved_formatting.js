// اختبار التحسينات الجديدة في التنسيق والتنظيم
const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

console.log('🚀 اختبار التحسينات الجديدة في التنسيق والتنظيم...');

async function testImprovedFormatting() {
    try {
        // إنشاء مثيل النظام
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء مثيل BugBountyCore بنجاح');

        // إنشاء ثغرة اختبار مع بيانات شاملة
        const testVuln = {
            name: 'SQL Injection في نموذج تسجيل الدخول المتقدم',
            type: 'SQL Injection',
            severity: 'high',
            url: 'https://testphp.vulnweb.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' UNION SELECT 1,2,3,4,5 --",
            response: 'MySQL Error: You have an error in your SQL syntax',
            evidence: 'تم تأكيد وجود SQL Injection من خلال رسائل الخطأ',
            cvss: '8.5',
            cwe: 'CWE-89'
        };

        console.log('📸 إنشاء الصور الفعلية للثغرة...');
        
        const reportId = `improved_test_${Date.now()}`;
        
        // إنشاء الصور الفعلية
        const screenshotFolder = await bugBountyCore.generateScreenshotsForVulnerabilities([testVuln], reportId);
        
        console.log(`✅ تم إنشاء مجلد الصور: ${screenshotFolder}`);

        console.log('🔥 تطبيق الدوال المحسنة...');
        
        // تطبيق الدوال المحسنة
        const realData = {
            url: testVuln.url,
            payload: testVuln.payload,
            response: testVuln.response,
            evidence: testVuln.evidence,
            parameter: testVuln.parameter
        };

        // تطبيق الدالة الأساسية المحسنة
        const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
        
        // تطبيق دالة خطوات الاستغلال المحسنة
        const exploitationSteps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
        
        // تطبيق دالة التأثير الديناميكي
        const dynamicImpact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);

        console.log('✅ تم تطبيق الدوال المحسنة بنجاح');

        // إنشاء تقرير HTML محسن مع التنسيق الجديد
        const htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير Bug Bounty v4.0 المحسن - التنسيق الجديد</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.2); }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 40px; text-align: center; }
        .header h1 { font-size: 3em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header .subtitle { font-size: 1.3em; opacity: 0.9; margin-bottom: 10px; }
        .content { padding: 40px; }
        .section { margin-bottom: 40px; padding: 30px; border-radius: 15px; box-shadow: 0 10px 20px rgba(0,0,0,0.1); }
        .section.summary { background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%); border-right: 5px solid #27ae60; }
        .section.vulnerabilities { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%); border-right: 5px solid #e17055; }
        .section.visual-changes { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border-right: 5px solid #17a2b8; }
        .section h2 { color: #2d3436; margin-bottom: 25px; font-size: 28px; border-bottom: 3px solid #667eea; padding-bottom: 15px; }
        .vulnerability-card { background: white; padding: 30px; border-radius: 15px; margin: 25px 0; border: 2px solid #e9ecef; box-shadow: 0 8px 16px rgba(0,0,0,0.1); }
        .vulnerability-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .function-section { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #007bff; }
        .function-content { background: white; padding: 15px; border-radius: 8px; margin-top: 10px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .stat-card { background: white; padding: 25px; border-radius: 10px; text-align: center; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 تقرير Bug Bounty v4.0 المحسن</h1>
            <p class="subtitle">التنسيق الجديد مع القالب الشامل التفصيلي</p>
            <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}</p>
            <p>مجلد الصور: ${screenshotFolder}</p>
        </div>
        
        <div class="content">
            <div class="section summary">
                <h2>📊 ملخص التحسينات الجديدة</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3 style="color: #27ae60;">✅ التنسيق المحسن</h3>
                        <p>تم تطبيق القالب الشامل التفصيلي</p>
                    </div>
                    <div class="stat-card">
                        <h3 style="color: #e17055;">📸 الصور الفعلية</h3>
                        <p>صور حقيقية مع تنسيق جميل</p>
                    </div>
                    <div class="stat-card">
                        <h3 style="color: #17a2b8;">🔧 الدوال المحسنة</h3>
                        <p>محتوى منظم ومفصل</p>
                    </div>
                </div>
            </div>
            
            <div class="section vulnerabilities">
                <h2>🎯 الثغرة المكتشفة مع التحسينات</h2>
                
                <div class="vulnerability-card">
                    <div class="vulnerability-header">
                        <h3>🚨 ${testVuln.name}</h3>
                        <p>نوع الثغرة: ${testVuln.type} | الخطورة: ${testVuln.severity}</p>
                    </div>
                    
                    <div class="function-section">
                        <h4>🔥 Function 1: generateComprehensiveDetailsFromRealData() - محسنة</h4>
                        <div class="function-content">
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${comprehensiveDetails}
                            </div>
                        </div>
                    </div>
                    
                    <div class="function-section">
                        <h4>⚡ Function 5: generateRealExploitationStepsForVulnerabilityComprehensive() - محسنة</h4>
                        <div class="function-content">
                            <div style="max-height: 400px; overflow-y: auto;">
                                ${exploitationSteps}
                            </div>
                        </div>
                    </div>
                    
                    <div class="function-section">
                        <h4>💥 Function 2: generateDynamicImpactForAnyVulnerability() - محسنة</h4>
                        <div class="function-content">
                            <div style="max-height: 300px; overflow-y: auto;">
                                ${dynamicImpact}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section visual-changes">
                <h2>📸 التحسينات في عرض الصور</h2>
                <div style="background: rgba(255,255,255,0.8); padding: 20px; border-radius: 12px;">
                    <h3>✅ التحسينات المطبقة:</h3>
                    <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
                        <li><strong>🎨 تنسيق جميل:</strong> تم تطبيق تنسيق القالب الشامل التفصيلي</li>
                        <li><strong>📸 صور فعلية:</strong> صور حقيقية مع معلومات المجلد</li>
                        <li><strong>🔧 دوال محسنة:</strong> محتوى منظم ومفصل لكل دالة</li>
                        <li><strong>📊 تحليل شامل:</strong> تفاصيل تقنية متقدمة</li>
                        <li><strong>⚡ أداء محسن:</strong> عرض سريع ومنظم</li>
                    </ul>
                </div>
            </div>
            
            <div style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; padding: 30px; border-radius: 15px; margin-top: 30px; text-align: center;">
                <h2>🎉 تم إكمال التحسينات بنجاح!</h2>
                <p style="font-size: 18px; margin: 15px 0;">التقارير الآن تستخدم التنسيق الجميل والمنظم كما في القالب الشامل التفصيلي</p>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-top: 20px;">
                    <p><strong>📁 مجلد الصور:</strong> ${screenshotFolder}</p>
                    <p><strong>📊 حجم المحتوى:</strong> ${(comprehensiveDetails.length / 1024).toFixed(2)} KB</p>
                    <p><strong>⚡ حالة التحسينات:</strong> مكتملة ومطبقة</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;

        // حفظ التقرير المحسن
        const fs = require('fs');
        const reportPath = `improved_formatting_report_${Date.now()}.html`;
        fs.writeFileSync(reportPath, htmlContent, 'utf8');

        console.log(`✅ تم إنشاء التقرير المحسن: ${reportPath}`);
        console.log(`📁 مجلد الصور: ${screenshotFolder}`);
        console.log(`📊 حجم التفاصيل الشاملة: ${(comprehensiveDetails.length / 1024).toFixed(2)} KB`);
        console.log(`⚡ حجم خطوات الاستغلال: ${(exploitationSteps.length / 1024).toFixed(2)} KB`);
        
        return { reportPath, screenshotFolder, comprehensiveDetails, exploitationSteps };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار التحسينات:', error);
        throw error;
    }
}

// تشغيل الاختبار
testImprovedFormatting()
    .then(result => {
        console.log(`\n🎉 تم إكمال اختبار التحسينات بنجاح!`);
        console.log(`📄 التقرير المحسن: ${result.reportPath}`);
        console.log(`📁 الصور: ${result.screenshotFolder}`);
        console.log(`\n💡 الآن التقارير تستخدم التنسيق الجميل والمنظم!`);
    })
    .catch(error => {
        console.error('❌ فشل اختبار التحسينات:', error);
    });
